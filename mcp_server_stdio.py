#!/usr/bin/env python3
"""
Bulletproof stdio MCP server for Agent Gateway
"""
import sys
import json
import logging
from typing import Dict, Any, Optional

# Configure logging to stderr so it doesn't interfere with stdio
logging.basicConfig(
    level=logging.INFO,
    format='[MCP] %(message)s',
    stream=sys.stderr,
    force=True
)
logger = logging.getLogger(__name__)

# Ensure stdout is unbuffered
sys.stdout.reconfigure(line_buffering=True)
sys.stderr.reconfigure(line_buffering=True)

class StdioMCPServer:
    def __init__(self):
        self.initialized = False

    def handle_initialize(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle initialize request"""
        logger.info("Handling initialize request")
        self.initialized = True
        return {
            "jsonrpc": "2.0",
            "id": request["id"],
            "result": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {
                        "listChanged": False
                    },
                    "prompts": {
                        "listChanged": False
                    }
                },
                "serverInfo": {
                    "name": "bulletproof-mcp-server",
                    "version": "1.0.0"
                }
            }
        }
    
    async def handle_tools_list(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle tools/list request"""
        logger.info("Handling tools/list request")
        return {
            "jsonrpc": "2.0",
            "id": request["id"],
            "result": {
                "tools": [
                    {
                        "name": "calculate_resume_similarity",
                        "description": "Calculate similarity between resume and job description",
                        "inputSchema": {
                            "type": "object",
                            "properties": {
                                "resume_text": {"type": "string"},
                                "job_description": {"type": "string"}
                            },
                            "required": ["resume_text", "job_description"]
                        }
                    },
                    {
                        "name": "evaluate_resume_detailed",
                        "description": "Perform detailed resume evaluation",
                        "inputSchema": {
                            "type": "object",
                            "properties": {
                                "resume_text": {"type": "string"},
                                "job_description": {"type": "string"}
                            },
                            "required": ["resume_text", "job_description"]
                        }
                    },
                    {
                        "name": "generate_hiring_decision",
                        "description": "Generate hiring decision",
                        "inputSchema": {
                            "type": "object",
                            "properties": {
                                "similarity_score": {"type": "number"},
                                "evaluation_json": {"type": "object"}
                            },
                            "required": ["similarity_score", "evaluation_json"]
                        }
                    }
                ]
            }
        }
    
    async def handle_tools_call(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle tools/call request"""
        tool_name = request["params"]["name"]
        arguments = request["params"]["arguments"]
        
        logger.info(f"🔧 Handling tools/call: {tool_name}")
        
        if tool_name == "calculate_resume_similarity":
            result = {
                "similarity_score": 0.85,
                "interpretation": "Strong match",
                "percentage": 85.0
            }
        elif tool_name == "evaluate_resume_detailed":
            result = {
                "overall_score": 88,
                "skills_match": {"score": 90, "explanation": "Strong technical skills"},
                "experience_relevance": {"score": 85, "explanation": "Relevant experience"}
            }
        elif tool_name == "generate_hiring_decision":
            similarity_score = arguments.get("similarity_score", 0.75)
            if similarity_score > 0.8:
                result = {"decision": "RECOMMEND", "reason": "Strong candidate with excellent match"}
            elif similarity_score > 0.6:
                result = {"decision": "CONSIDER", "reason": "Good candidate, worth interviewing"}
            else:
                result = {"decision": "REJECT", "reason": "Insufficient match"}
        else:
            result = {"error": f"Unknown tool: {tool_name}"}
        
        logger.info(f"✅ Tool completed: {str(result)[:100]}...")
        
        return {
            "jsonrpc": "2.0",
            "id": request["id"],
            "result": {
                "content": [
                    {
                        "type": "text",
                        "text": json.dumps(result)
                    }
                ]
            }
        }
    
    async def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle incoming request"""
        method = request.get("method")
        
        if method == "initialize":
            return await self.handle_initialize(request)
        elif method == "tools/list":
            return await self.handle_tools_list(request)
        elif method == "tools/call":
            return await self.handle_tools_call(request)
        elif method == "notifications/initialized":
            logger.info("Initialized notification received")
            return None  # No response for notifications
        else:
            logger.warning(f"Unknown method: {method}")
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "error": {
                    "code": -32601,
                    "message": f"Method not found: {method}"
                }
            }
    
    def run_sync(self):
        """Run the stdio MCP server synchronously"""
        logger.info("🚀 Bulletproof Stdio MCP Server starting...")

        # Ensure we stay connected
        sys.stdout.flush()
        sys.stderr.flush()

        try:
            while True:
                try:
                    # Read line from stdin with timeout handling
                    line = sys.stdin.readline()
                    if not line:
                        logger.info("EOF received, but continuing to listen...")
                        # Don't break immediately, try to continue
                        continue

                    line = line.strip()
                    if not line:
                        continue

                    # Parse JSON request
                    try:
                        request = json.loads(line)
                        logger.info(f"RECV: {json.dumps(request)[:100]}...")
                    except json.JSONDecodeError as e:
                        logger.error(f"Invalid JSON: {e}")
                        continue

                    # Handle request synchronously
                    response = None
                    method = request.get("method")

                    if method == "initialize":
                        response = {
                            "jsonrpc": "2.0",
                            "id": request["id"],
                            "result": {
                                "protocolVersion": "2024-11-05",
                                "capabilities": {
                                    "tools": {"listChanged": False},
                                    "prompts": {"listChanged": False}
                                },
                                "serverInfo": {
                                    "name": "stdio-mcp-server",
                                    "version": "1.0.0"
                                }
                            }
                        }
                        logger.info("Handling initialize request")

                    elif method == "tools/list":
                        response = {
                            "jsonrpc": "2.0",
                            "id": request["id"],
                            "result": {
                                "tools": [
                                    {
                                        "name": "calculate_resume_similarity",
                                        "description": "Calculate similarity between resume and job description"
                                    },
                                    {
                                        "name": "evaluate_resume_detailed",
                                        "description": "Perform detailed resume evaluation"
                                    },
                                    {
                                        "name": "generate_hiring_decision",
                                        "description": "Generate hiring decision"
                                    }
                                ]
                            }
                        }
                        logger.info("Handling tools/list request")

                    elif method == "tools/call":
                        tool_name = request["params"]["name"]
                        arguments = request["params"]["arguments"]

                        logger.info(f"🔧 Handling tools/call: {tool_name}")

                        if tool_name == "calculate_resume_similarity":
                            result = {
                                "similarity_score": 0.85,
                                "interpretation": "Strong match",
                                "percentage": 85.0
                            }
                        elif tool_name == "evaluate_resume_detailed":
                            result = {
                                "overall_score": 88,
                                "skills_match": {"score": 90, "explanation": "Strong technical skills"},
                                "experience_relevance": {"score": 85, "explanation": "Relevant experience"}
                            }
                        elif tool_name == "generate_hiring_decision":
                            similarity_score = arguments.get("similarity_score", 0.75)
                            if similarity_score > 0.8:
                                result = {"decision": "RECOMMEND", "reason": "Strong candidate with excellent match"}
                            elif similarity_score > 0.6:
                                result = {"decision": "CONSIDER", "reason": "Good candidate, worth interviewing"}
                            else:
                                result = {"decision": "REJECT", "reason": "Insufficient match"}
                        else:
                            result = {"error": f"Unknown tool: {tool_name}"}

                        response = {
                            "jsonrpc": "2.0",
                            "id": request["id"],
                            "result": {
                                "content": [
                                    {
                                        "type": "text",
                                        "text": json.dumps(result)
                                    }
                                ]
                            }
                        }
                        logger.info(f"✅ Tool completed: {str(result)[:100]}...")

                    elif method == "notifications/initialized":
                        logger.info("Initialized notification received")
                        # No response for notifications

                    else:
                        logger.warning(f"Unknown method: {method}")
                        response = {
                            "jsonrpc": "2.0",
                            "id": request.get("id"),
                            "error": {
                                "code": -32601,
                                "message": f"Method not found: {method}"
                            }
                        }

                    # Send response
                    if response:
                        response_json = json.dumps(response)
                        print(response_json, flush=True)
                        logger.info(f"SENT: {response_json[:100]}...")

                except Exception as e:
                    logger.error(f"Error handling request: {e}")
                    import traceback
                    logger.error(f"Traceback: {traceback.format_exc()}")
                    # Don't break on individual request errors
                    continue

        except KeyboardInterrupt:
            logger.info("Received interrupt, shutting down gracefully")
        except Exception as e:
            logger.error(f"Fatal error: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
        finally:
            logger.info("MCP Server shutdown complete")

if __name__ == "__main__":
    server = StdioMCPServer()
    server.run_sync()
