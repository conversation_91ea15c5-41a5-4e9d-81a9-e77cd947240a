#!/usr/bin/env python3
"""
Test both JSON and file upload endpoints
"""
import sys
import os

# Add project root to path
sys.path.append('/Users/<USER>/Documents/CES_MCP')

def test_endpoints():
    """Test both endpoints"""
    print("🧪 Testing API endpoints...")
    
    try:
        from app.main import app
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # Test health check
        print("\n1. Testing health check...")
        response = client.get("/api/health")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ Health check: {response.json()}")
        
        # Test JSON endpoint
        print("\n2. Testing JSON endpoint...")
        response = client.post(
            "/api/hr-assistant-unified",
            json={
                "query": "What makes a good software engineer?",
                "job_description": None,
                "resume_text": None,
                "url": None
            }
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ JSON endpoint works: {len(str(result))} chars response")
        else:
            print(f"   ❌ JSON endpoint failed: {response.text[:200]}...")
        
        # Test file upload endpoint with form data
        print("\n3. Testing file upload endpoint...")
        response = client.post(
            "/api/hr-assistant-unified-upload",
            data={
                "query": "What makes a good software engineer?",
                "resume_text": "Software engineer with 5 years Python experience",
                "job_description": "Looking for senior Python developer",
                "url": ""
            }
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Upload endpoint works: {len(str(result))} chars response")
        else:
            print(f"   ❌ Upload endpoint failed: {response.text[:200]}...")
        
        print("\n✅ Endpoint tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_endpoints()
