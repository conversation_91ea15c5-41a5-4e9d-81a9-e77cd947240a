from fastapi import FastAP<PERSON>
from contextlib import asynccontextmanager
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.executors.pool import ProcessPoolExecutor

from app.orchestrator.scheduler import train_and_save_model
from app.orchestrator.jury import jury
from app.settings import settings
from app.api.v1.unified_hr_assistant_route import router as unified_hr_router
import logfire

logfire.configure(token=settings.LOGFIRE_TOKEN)

# Initialize the scheduler
executors = {
    'default': ProcessPoolExecutor(max_workers=2)
}
scheduler = AsyncIOScheduler(executors=executors)


@asynccontextmanager
async def lifespan(app: FastAPI):
    logfire.info("scheduler_start", details={"message": "Scheduler is starting..."})
    scheduler.add_job(train_and_save_model, 'interval', minutes=5)
    scheduler.add_job(jury, 'interval', minutes=5)
    scheduler.start()

    # Note: Pre-warming disabled to avoid startup delays
    # Components will load lazily on first request
    logfire.info("mcp_ready", details={"message": "MCP integration ready (lazy loading enabled)"})

    yield
    scheduler.shutdown()
    logfire.info("scheduler_stop", details={"message": "Scheduler has stopped."})


app = FastAPI(lifespan=lifespan)

# Instrument FastAPI with Logfire (v3+ way)
logfire.instrument_fastapi(app, capture_headers=True)


# app.include_router(evaluate_router)
# app.include_router(hr_router)
# app.include_router(info_extracter_router)

app.include_router(unified_hr_router, prefix="/api")
