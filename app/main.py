from fastapi import <PERSON>AP<PERSON>, Request
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from contextlib import asynccontextmanager
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.executors.pool import ProcessPoolExecutor
import logging

from app.orchestrator.scheduler import train_and_save_model
from app.orchestrator.jury import jury
from app.settings import settings
from app.api.v1.unified_hr_assistant_route import router as unified_hr_router
import logfire

logger = logging.getLogger(__name__)

logfire.configure(token=settings.LOGFIRE_TOKEN)

# Initialize the scheduler
executors = {
    'default': ProcessPoolExecutor(max_workers=2)
}
scheduler = AsyncIOScheduler(executors=executors)


@asynccontextmanager
async def lifespan(app: FastAPI):
    logfire.info("scheduler_start", details={"message": "Scheduler is starting..."})
    scheduler.add_job(train_and_save_model, 'interval', minutes=5)
    scheduler.add_job(jury, 'interval', minutes=5)
    scheduler.start()

    # Check MCP connection status
    try:
        from app.mcp_module.client import gateway
        import httpx

        # Test if Agent Gateway is reachable
        try:
            async with httpx.AsyncClient(timeout=2.0) as client:
                response = await client.get(f"{gateway.gateway_url}/health", timeout=2.0)
                if response.status_code == 200:
                    logfire.info("mcp_status", details={
                        "message": "✅ Agent Gateway is reachable",
                        "gateway_url": gateway.gateway_url,
                        "status": "online"
                    })
                else:
                    logfire.warning("mcp_status", details={
                        "message": "⚠️ Agent Gateway responded but not healthy",
                        "gateway_url": gateway.gateway_url,
                        "status_code": response.status_code
                    })
        except Exception:
            # Try SSE endpoint instead
            try:
                async with httpx.AsyncClient(timeout=2.0) as client:
                    async with client.stream("GET", f"{gateway.gateway_url}/sse", timeout=2.0) as response:
                        if response.status_code == 200:
                            logfire.info("mcp_status", details={
                                "message": "✅ Agent Gateway SSE endpoint is reachable",
                                "gateway_url": gateway.gateway_url,
                                "status": "online"
                            })
                        else:
                            logfire.warning("mcp_status", details={
                                "message": "⚠️ Agent Gateway SSE endpoint not responding",
                                "gateway_url": gateway.gateway_url,
                                "status": "offline"
                            })
            except Exception as sse_e:
                logfire.warning("mcp_status", details={
                    "message": "❌ Agent Gateway not reachable",
                    "gateway_url": gateway.gateway_url,
                    "error": str(sse_e),
                    "status": "offline"
                })

    except Exception as e:
        logfire.error("mcp_error", details={
            "message": "MCP initialization failed",
            "error": str(e)
        })

    # Note: Pre-warming disabled to avoid startup delays
    # Components will load lazily on first request
    logfire.info("mcp_ready", details={"message": "MCP integration ready (lazy loading enabled)"})

    yield
    scheduler.shutdown()
    logfire.info("scheduler_stop", details={"message": "Scheduler has stopped."})


app = FastAPI(lifespan=lifespan)

# Custom exception handler for UnicodeDecodeError in request validation
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle validation errors that might contain binary data"""
    try:
        # Try to get a safe error representation
        errors = []
        for error in exc.errors():
            safe_error = {}
            for key, value in error.items():
                if key == 'input' and isinstance(value, bytes):
                    # Replace binary data with a safe representation
                    safe_error[key] = f"<binary data: {len(value)} bytes>"
                elif isinstance(value, bytes):
                    safe_error[key] = f"<binary: {len(value)} bytes>"
                else:
                    safe_error[key] = value
            errors.append(safe_error)

        return JSONResponse(
            status_code=422,
            content={"detail": errors}
        )
    except Exception as e:
        logger.error(f"Error in validation exception handler: {e}")
        return JSONResponse(
            status_code=422,
            content={"detail": "Request validation failed due to encoding issues"}
        )

# Instrument FastAPI with Logfire (v3+ way)
logfire.instrument_fastapi(app, capture_headers=True)


# app.include_router(evaluate_router)
# app.include_router(hr_router)
# app.include_router(info_extracter_router)

app.include_router(unified_hr_router, prefix="/api")
