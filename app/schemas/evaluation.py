from typing import Optional, Dict, Any

from pydantic.v1 import BaseModel

class UnifiedAgentState(BaseModel):
    # Input fields
    query: Optional[str] = None
    resume_text: Optional[str] = None
    job_description: Optional[str] = None
    url: Optional[str] = None
    user_id: Optional[int] = None

    # Intent classification
    intent: Optional[str] = None
    intent_confidence: Optional[float] = None

    # Resume evaluation fields
    similarity_score: Optional[float] = None
    evaluation_json: Optional[Dict[str, Any]] = None

    # HR chat fields
    response: Optional[str] = None

    # Portal evaluation fields
    candidates_data: Optional[list] = None
    portal_results: Optional[Dict[str, Any]] = None

    # Common fields
    status: Optional[str] = None
    notes: Optional[str] = None
    error: Optional[str] = None

    # Workflow control
    next_action: Optional[str] = None
    completed_actions: Optional[list] = None

