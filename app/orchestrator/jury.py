from verdict.util import ratelimit
from verdict.model import vLL<PERSON>odel
from verdict import <PERSON><PERSON><PERSON>, Layer
from verdict.common.judge import CategoricalJudgeUnit,JudgeUnit
from verdict.common.conversational import ConversationalUnit
from verdict.scale import DiscreteScale,BooleanScale
from verdict.schema import Schema
from supabase import create_client, Client
from app.settings import settings
import os
import logfire


SUPABASE_URL = settings.SUPABASE_URL
SUPABASE_KEY = settings.SUPABASE_API_KEY

supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
def jury():
    os.environ["OPENAI_API_KEY"] = settings.OPENAI_API_KEY
    logfire.info("Jury function started", step="start")
    ratelimit.disable()
    logfire.info("Rate limiting disabled")
    print("Running the jury ...")
    logfire.info("Fetching unevaluated results")
    response = supabase.table("evaluation_results").select("*").is_("jury_flag", None).is_("jury_critic", None).execute()
    if response.data:
        logfire.info("Data retrieved", count=len(response.data))
        pipeline = Pipeline() \
            >> Layer([
                ConversationalUnit(role_name='Proponent').prompt("""
                    You are participating in a debate as the Proponent.

                    Your task is to argue that the AI assistant’s EVALUATIONRESULT is appropriate and well-aligned with both the JOBDESCRIPTION and the RESUMETEXT.

                    RESUMETEXT:
                    {source.resume_text}

                    JOBDESCRIPTION:
                    {source.job_description}

                    EVALUATIONRESULT:
                    {source.evaluation_result}

                    Debate so far:
                    {input.conversation}
                    """),
                ConversationalUnit(role_name='Opponent').prompt("""
                    You are participating in a debate as the Opponent.

                    Your task is to argue that the AI assistant’s EVALUATIONRESULT is **not** appropriate and does **not** align with the JOBDESCRIPTION and the RESUMETEXT.

                    RESUMETEXT:
                    {source.resume_text}

                    JOBDESCRIPTION:
                    {source.job_description}

                    EVALUATIONRESULT:
                    {source.evaluation_result}

                    Debate so far:
                    {input.conversation}
                    """)
            ], inner='chain', outer='broadcast').with_leaf([0, 1]) \
            >> CategoricalJudgeUnit(name='judge', explanation=True, categories=DiscreteScale(['Pass', 'Fail'])).prompt("""
                You are the final judge in a debate over whether an AI assistant’s evaluation of a candidate is correct.

                You must assess whether the **EVALUATIONRESULT** is appropriately derived from the **RESUMETEXT** and the **JOBDESCRIPTION**.

                Use the following criteria to guide your decision:
                1. Does the evaluation meaningfully and accurately reflect the skills, experiences, or requirements in the resume and job description?
                2. Are there any significant mismatches or unsupported conclusions?
                3. Is the AI’s evaluation logical, fair, and specific?

                RESUMETEXT:
                {source.resume_text}

                JOBDESCRIPTION:
                {source.job_description}

                EVALUATIONRESULT:
                {source.evaluation_result}

                Debater #1:
                {previous.conversational[0].response}

                Debater #2:
                {previous.conversational[1].response}

                Please respond in the following format:

                Decision: Pass / Fail  
                Explanation: [Your reasoning based on the debate and criteria above]
                """)

        for result in response.data:
            
            logfire.info("Processing evaluation result", result_id=result["id"])
            result_interm, leaf_node_prefixes = pipeline.run(Schema.of(resume_text=result['resume_text'],job_description=result['job_description'],evaluation_result=f'{result['evaluation_result']}'
            ))


            jury_flag = True if result_interm["Pipeline_root.block.block.unit[CategoricalJudge judge]_choice"].strip().lower() == 'pass' else False
            jury_critic = result_interm["Pipeline_root.block.block.unit[CategoricalJudge judge]_explanation"]
            if jury_flag == 0:
              logfire.warning("Pipeline judgment complete with jury_flag=0", result_id=result["id"], jury_flag=jury_flag)
            else:
              logfire.info("Pipeline judgment complete", result_id=result["id"], jury_flag=jury_flag)
            f_result=supabase.table("evaluation_results").update({"jury_flag": jury_flag,"jury_critic": jury_critic}).eq("id", result["id"]).execute()

    else:
        logfire.error("No records found for evaluation")
    logfire.info("Jury function finished", step="end")
