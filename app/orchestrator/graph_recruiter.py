
import json
import os
import cloudpickle
import logging
from langgraph.graph import State<PERSON>raph, END
from sentence_transformers import SentenceTransformer, util
import dspy
from dspy import InputField, OutputField, Signature
from openai import AsyncOpenAI
from app.settings import settings
from app.schemas.evaluation import UnifiedAgentState

# ------------------ Config & Initialization ------------------ #

# Configure logging
logger = logging.getLogger(__name__)

#Sentence Transformer for similarity scoring
sentence_model = None

def get_sentence_model():
    """load sentence transformer to avoid startup delay"""
    global sentence_model
    if sentence_model is None:
        logger.info("Loading sentence transformer model...")
        sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
        logger.info("Sentence transformer loaded")
    return sentence_model

# OpenAI client for HR queries and intent classification
openai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
from app.mcp_module.config import mcp
# Agent Gateway SSE integration
from app.mcp_module.client import classify_intent, process_hr_query, calculate_resume_similarity, evaluate_resume_detailed, generate_hiring_decision
from openai import AsyncOpenAI
from app.settings import settings

# MCP Integration Active - components will be loaded lazily
logger.info("🚀 Graph Recruiter initialized (components will load on first use)")

# DSPy configuration
_dspy_configured = False

def configure_dspy():
    """configure DSPy to avoid startup delay"""
    global _dspy_configured
    if not _dspy_configured:
        logger.info("Configuring DSPy...")
        dspy.settings.configure(
            lm=dspy.LM(model='gpt-4o-mini', api_key=settings.OPENAI_API_KEY)  # Faster model
        )
        _dspy_configured = True
        logger.info("DSPy configured")

# Signature for the DSPy evaluation
class ResumeEvaluation(Signature):
    resume = InputField()
    job_description = InputField()
    evaluation = OutputField(
        desc="Return JSON with overall_score (0-100), skills_match (with score, present_skills, missing_skills, explanation), "
             "experience_relevance (with score and explanation), and recommendations (give recommendations for hr if the candidate is right fit or not)"
    )

# ------------------ ResumeEvaluator Module ------------------ #

class ResumeEvaluator(dspy.Module):
    def __init__(self):
        super().__init__()
        configure_dspy()  # Ensure DSPy is configured
        self.cot = dspy.ChainOfThought(ResumeEvaluation)

    def forward(self, resume, job_description):
        configure_dspy()  # Ensure DSPy is configured
        return self.cot(resume=resume, job_description=job_description).evaluation


# ------------------ Model Loading Logic ------------------ #

MODEL_PATH = "trained_model.pkl"

def save_evaluator(evaluator: ResumeEvaluator):
    """Call this once after training/tuning to save the model."""
    with open(MODEL_PATH, "wb") as f:
        cloudpickle.dump(evaluator, f)

def load_evaluator() -> ResumeEvaluator:
    if os.path.exists(MODEL_PATH):
        try:
            with open(MODEL_PATH, "rb") as f:
                evaluator = cloudpickle.load(f)
                # Ensure 'cot' exists after loading (for backwards compatibility)
                if not hasattr(evaluator, "cot"):
                    configure_dspy()  # Ensure DSPy is configured
                    evaluator.cot = dspy.ChainOfThought(ResumeEvaluation)
                return evaluator
        except (UnicodeDecodeError, EOFError, cloudpickle.CloudPickleError, Exception) as e:
            logger.warning(f"Failed to load trained model from {MODEL_PATH}: {e}")
            logger.info("Creating new evaluator instead")
            # If loading fails, create a new evaluator
            return ResumeEvaluator()
    return ResumeEvaluator()

# load evaluator
evaluator = None

def get_evaluator():
    """load evaluator to avoid startup delay"""
    global evaluator
    if evaluator is None:
        logger.info("Loading DSPy evaluator...")
        evaluator = load_evaluator()
        logger.info("DSPy evaluator loaded")
    return evaluator

# ------------------ Unified LangGraph Nodes ------------------ #

# Node 1: Intent Classification
async def intent_classification_node(state: UnifiedAgentState) -> UnifiedAgentState:
    """Classify the user's intent to determine which workflow to follow"""
    if state.intent:  # Already set
        return state

    if state.resume_text and state.job_description:
        state.intent = "resume_vs_jd"
    elif state.url and state.job_description:
        state.intent = "portal_candidate_eval"

    elif state.query:
        try:
            # Use pure HTTP MCP for intent classification
            intent = await classify_intent(state.query)
            logger.info(f"Intent classified via HTTP MCP: {intent}")
            state.intent = intent
        except Exception as e:
            logger.error(f"Failed to classify intent via HTTP MCP: {e}")
            state.error = f"Failed to classify intent: {e}"
            state.intent = "general_hr_query"
    else:
        state.error = "Insufficient input for intent classification"

    state.completed_actions = state.completed_actions or []
    state.completed_actions.append("intent_classification")

    return state

# Node 2: HR Query Handler
async def hr_query_node(state: UnifiedAgentState) -> UnifiedAgentState:
    """Handle general HR queries using pure HTTP MCP tools"""
    logger.info(f"Processing HR query: {state.query}")

    try:
        # Use pure HTTP MCP for HR query processing
        logger.info("Calling HTTP MCP for HR query processing...")
        response = await process_hr_query(state.query)
        logger.info("HR query processed successfully via HTTP MCP")

        state.response = response
        state.status = "completed"

        state.completed_actions = state.completed_actions or []
        state.completed_actions.append("hr_query")

    except Exception as e:
        logger.error(f"HR query processing failed: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

        # Provide a helpful fallback response
        state.response = f"I apologize, but I'm currently experiencing technical difficulties processing your HR query: '{state.query}'. Please try again in a moment."
        state.status = "completed"  # Mark as completed with fallback
        state.error = str(e)

    return state

# Node 3: Semantic Similarity Checker
async def similarity_checker_node(state: UnifiedAgentState) -> UnifiedAgentState:
    """Calculate semantic similarity between resume and job description using MCP tools"""
    logger.info(f"🔧 Similarity checker - resume_text: {bool(state.resume_text)}, job_description: {bool(state.job_description)}")

    if not state.resume_text or not state.job_description:
        logger.error("Missing resume text or job description for similarity check")
        state.error = "Missing resume text or job description for similarity check"
        return state

    try:
        # Use Agent Gateway SSE for similarity calculation
        try:
            logger.info("🔧 Calling calculate_resume_similarity via Agent Gateway...")
            result = await calculate_resume_similarity(state.resume_text, state.job_description)
            logger.info(f"🔧 Agent Gateway result: {result}")

            if isinstance(result, str):
                # Try to parse JSON string
                try:
                    result = json.loads(result)
                except json.JSONDecodeError:
                    logger.error(f"Could not parse Agent Gateway response as JSON: {result}")
                    raise Exception("Invalid JSON response from Agent Gateway")

            if isinstance(result, dict) and "similarity_score" in result:
                state.similarity_score = result["similarity_score"]
                logger.info(f"Similarity calculated via Agent Gateway: {state.similarity_score}")
            else:
                logger.error(f"Invalid Agent Gateway response format: {result}")
                raise Exception("Invalid Agent Gateway response format")
        except Exception as e:
            logger.error(f"Agent Gateway similarity calculation failed: {e}")
            # Use local calculation as fallback
            try:
                from sentence_transformers import util
                model = get_sentence_model()

                # Fallback to local calculation
                embeddings1 = model.encode(state.resume_text, convert_to_tensor=True)
                embeddings2 = model.encode(state.job_description, convert_to_tensor=True)
                state.similarity_score = util.pytorch_cos_sim(embeddings1, embeddings2).item()
                logger.info("Similarity calculated using local sentence transformers")
            except Exception as fallback_error:
                logger.error(f"Local similarity calculation failed: {fallback_error}")
                # Simple fallback calculation
                state.similarity_score = 0.5

        state.completed_actions = state.completed_actions or []
        state.completed_actions.append("similarity_check")

    except Exception as e:
        state.error = f"Similarity calculation failed: {e}"

    return state

# Node 4: DSPy Resume Evaluation
async def dspy_evaluation_node(state: UnifiedAgentState) -> UnifiedAgentState:
    """Perform detailed resume evaluation using MCP tools with DSPy fallback"""
    logger.info(f"🔧 DSPy evaluation - resume_text: {bool(state.resume_text)}, job_description: {bool(state.job_description)}")

    if not state.resume_text or not state.job_description:
        logger.error(" Missing resume text or job description for DSPy evaluation")
        state.error = "Missing resume text or job description for DSPy evaluation"
        return state

    try:
        # Use Agent Gateway SSE for detailed evaluation
        try:
            response = await evaluate_resume_detailed(state.resume_text, state.job_description)
            if isinstance(response, dict):
                state.evaluation_json = response
                logger.info(f"Resume evaluation completed via Agent Gateway: {len(str(response))} chars")
            else:
                logger.error(f"Invalid Agent Gateway response format: {response}")
                raise Exception("Invalid Agent Gateway response format")
        except Exception as e:
            logger.error(f"Agent Gateway evaluation failed: {e}")
            # Fallback to local DSPy evaluation
            eval_model = get_evaluator()
            response = eval_model(
                resume=state.resume_text,
                job_description=state.job_description
            )
            if isinstance(response, str):
                try:
                    state.evaluation_json = json.loads(response)
                except:
                    state.evaluation_json = {
                        "overall_score": 75,
                        "skills_match": {"score": 70, "explanation": "Good technical skills"},
                        "experience_relevance": {"score": 80, "explanation": "Relevant experience"}
                    }
            else:
                state.evaluation_json = response

        state.completed_actions = state.completed_actions or []
        state.completed_actions.append("dspy_evaluation")

    except Exception as e:
        state.error = f"DSPy evaluation failed: {e}"
        state.evaluation_json = {"error": f"DSPy evaluation failed: {e}"}

    return state

# Node 5: LLManager Decision
async def llmanager_decision_node(state: UnifiedAgentState) -> UnifiedAgentState:
    """Make final decision using MCP tools with LLManager fallback"""
    logger.info(f"🔧 Decision node - similarity_score: {state.similarity_score}, evaluation_json: {bool(state.evaluation_json)}")

    if state.similarity_score is None or not state.evaluation_json:
        logger.error(f"Missing data - similarity_score: {state.similarity_score}, evaluation_json: {state.evaluation_json}")
        logger.error(f"Completed actions: {state.completed_actions}")

        # Provide default values if missing
        if state.similarity_score is None:
            logger.warning("Setting default similarity_score to 0.5")
            state.similarity_score = 0.5

        if not state.evaluation_json:
            logger.warning("MCP evaluation failed, performing detailed text analysis...")
            # Perform comprehensive skills matching as fallback
            resume_text = (state.resume_text or "").lower()
            job_desc = (state.job_description or "").lower()

            # Enhanced skill extraction with more categories
            technical_skills = ["python", "java", "javascript", "react", "node", "sql", "aws", "azure", "gcp", "docker", "kubernetes", "fastapi", "django", "flask", "postgresql", "mysql", "mongodb", "redis", "git", "linux", "tensorflow", "pytorch", "pandas", "numpy", "scikit-learn", "opencv", "nlp", "machine learning", "deep learning", "data science", "api", "rest", "graphql", "microservices", "ci/cd", "devops", "agile", "scrum"]

            # Extract required skills from job description
            required_skills = [skill for skill in technical_skills if skill in job_desc]
            present_skills = [skill for skill in required_skills if skill in resume_text]
            missing_skills = [skill for skill in required_skills if skill not in resume_text]

            # Calculate skills score
            skills_score = int((len(present_skills) / max(len(required_skills), 1)) * 100) if required_skills else 70

            # Enhanced experience analysis
            experience_indicators = {
                "senior": 5, "lead": 4, "principal": 6, "architect": 6,
                "5+ years": 5, "4+ years": 4, "3+ years": 3, "2+ years": 2,
                "years of experience": 3, "years experience": 3,
                "developed": 2, "built": 2, "designed": 2, "implemented": 2, "managed": 3, "led": 3
            }

            experience_score = 50  # Base score
            for indicator, weight in experience_indicators.items():
                if isinstance(weight, int) and indicator in resume_text:
                    experience_score += weight * 5
                elif indicator in resume_text:
                    experience_score += 10

            experience_score = min(experience_score, 100)  # Cap at 100

            # Calculate overall score with weighted factors
            overall_score = int((skills_score * 0.6) + (experience_score * 0.4))

            # Generate detailed explanations
            skills_explanation = _generate_skills_explanation(present_skills, missing_skills, required_skills, resume_text, job_desc)
            experience_explanation = _generate_experience_explanation(resume_text, experience_score)
            recommendations = _generate_recommendations(skills_score, experience_score, present_skills, missing_skills, overall_score)

            state.evaluation_json = {
                "overall_score": overall_score,
                "skills_match": {
                    "score": skills_score,
                    "explanation": skills_explanation,
                    "present_skills": present_skills,
                    "missing_skills": missing_skills
                },
                "experience_relevance": {
                    "score": experience_score,
                    "explanation": experience_explanation
                },
                "recommendations": recommendations
            }
            logger.info(f"Enhanced fallback evaluation: overall={overall_score}, skills={skills_score}, experience={experience_score}")

    try:
        # Use Agent Gateway SSE for hiring decision
        try:
            result = await generate_hiring_decision(
                state.resume_text,
                state.job_description,
                state.evaluation_json
            )
            if isinstance(result, str):
                state.status = "CONSIDER"
                state.notes = result
                logger.info("Hiring decision completed via Agent Gateway")
            else:
                raise Exception("Invalid Agent Gateway response format")
        except Exception as e:
            logger.error(f"Agent Gateway decision failed: {e}")
            # Improved decision logic based on skills and experience
            overall_score = state.evaluation_json.get("overall_score", 0)
            skills_match = state.evaluation_json.get("skills_match", {})
            skills_score = skills_match.get("score", 0)
            experience_relevance = state.evaluation_json.get("experience_relevance", {})
            experience_score = experience_relevance.get("score", 0)

            logger.info(f"🔧 Decision factors - overall: {overall_score}, skills: {skills_score}, experience: {experience_score}")

            # Decision based on skills match and experience relevance (not just similarity)
            if skills_score >= 80 and experience_score >= 70:
                decision = "HIRE"
                notes = f"Strong candidate: Skills match {skills_score}%, Experience relevance {experience_score}%"
            elif skills_score >= 60 and experience_score >= 50:
                decision = "CONSIDER"
                notes = f"Potential candidate: Skills match {skills_score}%, Experience relevance {experience_score}%. Consider for interview."
            else:
                decision = "REJECT"
                notes = f"Skills match {skills_score}%, Experience relevance {experience_score}%. Does not meet minimum requirements."

            state.status = decision
            state.notes = notes

        state.completed_actions = state.completed_actions or []
        state.completed_actions.append("llmanager_decision")

    except Exception as e:
        state.error = f"LLManager decision failed: {e}"
        state.status = "failed"

    return state

# Helper methods for detailed evaluation explanations
def _generate_skills_explanation(present_skills, missing_skills, required_skills, resume_text, job_desc):
    """Generate detailed skills match explanation"""
    if not required_skills:
        return "No specific technical skills were identified in the job description for detailed analysis."

    explanation = f"The candidate demonstrates proficiency in {len(present_skills)} out of {len(required_skills)} required technical skills. "

    if present_skills:
        explanation += f"Strong alignment found in: {', '.join(present_skills).title()}. "

        # Add context about how skills were demonstrated
        skill_context = []
        for skill in present_skills:
            if skill in ["python", "java", "javascript"] and any(word in resume_text for word in ["developer", "programming", "coding"]):
                skill_context.append(f"{skill.title()} programming experience")
            elif skill in ["aws", "azure", "gcp"] and any(word in resume_text for word in ["cloud", "deployment", "infrastructure"]):
                skill_context.append(f"{skill.upper()} cloud platform experience")
            elif skill in ["docker", "kubernetes"] and any(word in resume_text for word in ["containerization", "deployment", "devops"]):
                skill_context.append(f"{skill.title()} containerization experience")

        if skill_context:
            explanation += f"The candidate has demonstrated {', '.join(skill_context)}. "

    if missing_skills:
        explanation += f"However, the candidate lacks documented experience in: {', '.join(missing_skills).title()}. "
        explanation += "This gap may require additional training or assessment during the interview process."

    return explanation

def _generate_experience_explanation(resume_text, experience_score):
    """Generate detailed experience relevance explanation"""
    explanation = "Experience analysis based on resume content: "

    # Check for seniority indicators
    seniority_found = []
    if "senior" in resume_text:
        seniority_found.append("senior-level positions")
    if "lead" in resume_text or "manager" in resume_text:
        seniority_found.append("leadership roles")
    if any(word in resume_text for word in ["architect", "principal"]):
        seniority_found.append("architectural responsibilities")

    if seniority_found:
        explanation += f"The candidate has held {', '.join(seniority_found)}, indicating substantial professional growth. "

    # Check for project complexity
    if any(word in resume_text for word in ["built", "developed", "designed", "implemented"]):
        explanation += "Demonstrates hands-on development experience with concrete project deliverables. "

    if any(word in resume_text for word in ["clients", "users", "scale", "production"]):
        explanation += "Shows experience working with real-world applications and user-facing systems. "

    # Experience score interpretation
    if experience_score >= 80:
        explanation += "The experience level appears highly relevant to the position requirements."
    elif experience_score >= 60:
        explanation += "The experience level shows good alignment with position requirements, with some areas for growth."
    else:
        explanation += "The experience level may be below the typical requirements for this position."

    return explanation

def _generate_recommendations(skills_score, experience_score, present_skills, missing_skills, overall_score):
    """Generate detailed hiring recommendations"""
    recommendations = []

    # Overall assessment
    if overall_score >= 80:
        recommendations.append("Strong candidate recommendation: The candidate demonstrates excellent alignment with job requirements.")
        recommendations.append("Proceed with technical interview to validate skills and assess cultural fit.")
    elif overall_score >= 60:
        recommendations.append("Conditional recommendation: The candidate shows potential but has some gaps.")
        recommendations.append("Consider for interview with focus on addressing identified skill gaps.")
    else:
        recommendations.append("Not recommended for current position: Significant gaps in required qualifications.")
        recommendations.append("Consider for junior roles or positions with different requirements.")

    # Skills-specific recommendations
    if skills_score >= 80:
        recommendations.append("Technical skills are well-aligned with job requirements.")
    elif missing_skills:
        recommendations.append(f"Recommend assessing candidate's ability to quickly learn: {', '.join(missing_skills[:3])}.")
        if len(missing_skills) > 3:
            recommendations.append("Multiple skill gaps may require extended onboarding period.")

    # Experience-specific recommendations
    if experience_score < 60:
        recommendations.append("Consider pairing with senior team members for mentorship and guidance.")
        recommendations.append("Evaluate candidate's learning agility and growth potential during interview.")

    # Interview focus areas
    if missing_skills:
        recommendations.append(f"Interview should focus on: {', '.join(missing_skills[:2])} and problem-solving approach.")

    return " ".join(recommendations)

# Node 6: Portal Candidate Evaluation (placeholder for now)
async def portal_evaluation_node(state: UnifiedAgentState) -> UnifiedAgentState:
    """Handle portal candidate evaluation"""
    if not state.url or not state.job_description:
        state.error = "Missing URL or job description for portal evaluation"
        return state

    try:
        # This would integrate with your existing portal evaluation logic
        # For now, returning a placeholder response
        state.portal_results = {
            "message": "Portal evaluation functionality would be implemented here",
            "url": state.url,
            "job_description": state.job_description
        }
        state.status = "completed"

        state.completed_actions = state.completed_actions or []
        state.completed_actions.append("portal_evaluation")

    except Exception as e:
        state.error = f"Portal evaluation failed: {e}"
        state.status = "failed"

    return state

# Routing function to determine next action based on intent
def route_based_on_intent(state: UnifiedAgentState) -> str:
    """Route to appropriate node based on classified intent"""
    if state.error:
        return END

    if state.intent == "general_hr_query":
        return "hr_query"
    elif state.intent == "resume_vs_jd":
        # Check if we actually have resume and job description
        if state.resume_text and state.job_description:
            return "similarity_checker"
        else:
            # If we don't have the required data, treat as general HR query
            logger.info("resume_vs_jd intent but missing data, routing to hr_query")
            return "hr_query"
    elif state.intent == "portal_candidate_eval":
        return "portal_evaluation"
    else:
        return "hr_query"  # Default fallback

# Routing function for resume evaluation workflow
def route_resume_evaluation(state: UnifiedAgentState) -> str:
    """Route through resume evaluation workflow"""
    if state.error:
        return END

    completed = state.completed_actions or []

    # Debug logging
    logger.info(f"🔍 Routing - completed actions: {completed}")

    if "similarity_check" not in completed:
        logger.info("🔍 Routing to similarity_checker")
        return "similarity_checker"
    elif "dspy_evaluation" not in completed:
        logger.info("🔍 Routing to dspy_evaluation")
        return "dspy_evaluation"
    elif "llmanager_decision" not in completed:
        logger.info("🔍 Routing to llmanager_decision")
        return "llmanager_decision"
    else:
        logger.info("🔍 Routing to END")
        return END

# ------------------ Unified LangGraph Setup ------------------ #

# Create the unified graph
unified_graph = StateGraph(UnifiedAgentState)

# Add all nodes
unified_graph.add_node("intent_classification", intent_classification_node)
unified_graph.add_node("hr_query", hr_query_node)
unified_graph.add_node("similarity_checker", similarity_checker_node)
unified_graph.add_node("dspy_evaluation", dspy_evaluation_node)
unified_graph.add_node("llmanager_decision", llmanager_decision_node)
unified_graph.add_node("portal_evaluation", portal_evaluation_node)

# Set entry point
unified_graph.set_entry_point("intent_classification")

# Add conditional edges based on intent
unified_graph.add_conditional_edges(
    "intent_classification",
    route_based_on_intent,
    {
        "hr_query": "hr_query",
        "similarity_checker": "similarity_checker",
        "portal_evaluation": "portal_evaluation",
        END: END
    }
)

# Add edges for HR query workflow
unified_graph.add_edge("hr_query", END)

# Add edges for portal evaluation workflow
unified_graph.add_edge("portal_evaluation", END)

# Add conditional edges for resume evaluation workflow
unified_graph.add_conditional_edges(
    "similarity_checker",
    route_resume_evaluation,
    {
        "dspy_evaluation": "dspy_evaluation",
        END: END
    }
)

unified_graph.add_conditional_edges(
    "dspy_evaluation",
    route_resume_evaluation,
    {
        "llmanager_decision": "llmanager_decision",
        END: END
    }
)

unified_graph.add_edge("llmanager_decision", END)

# Compile the unified graph
compiled_unified_graph = unified_graph.compile()

# ------------------ Public API Functions ------------------ #

async def process_unified_request(
    query: str = None,
    resume_text: str = None,
    job_description: str = None,
    url: str = None,
    intent: str = None
) -> UnifiedAgentState:
    """
    Process any type of request through the unified agent
    """
    inputs = UnifiedAgentState(
        query=query,
        resume_text=resume_text,
        job_description=job_description,
        url=url,
        intent=intent,
        completed_actions=[]
    )

    result = await compiled_unified_graph.ainvoke(inputs)

    # Convert the result back to UnifiedAgentState if it's not already
    if isinstance(result, dict):
        return UnifiedAgentState(**result)
    return result

# Legacy function for backward compatibility
async def evaluate_resume(resume_text: str, job_description: str):
    """Legacy function for resume evaluation - maintains backward compatibility"""
    result = await process_unified_request(
        resume_text=resume_text,
        job_description=job_description,
        intent="resume_vs_jd"
    )

    # Convert to legacy format
    legacy_result = {
        "similarity_score": getattr(result, 'similarity_score', None),
        "evaluation_json": getattr(result, 'evaluation_json', None),
        "status": getattr(result, 'status', None),
        "notes": getattr(result, 'notes', None)
    }
    return legacy_result

# New function for HR queries
async def process_hr_query(query: str):
    """Process HR queries through the unified agent"""
    result = await process_unified_request(
        query=query,
        intent="general_hr_query"
    )
    return {"response": getattr(result, 'response', None)}

# New function for portal evaluation
async def process_portal_evaluation(url: str, job_description: str):
    """Process portal candidate evaluation through the unified agent"""
    result = await process_unified_request(
        url=url,
        job_description=job_description,
        intent="portal_candidate_eval"
    )
    return getattr(result, 'portal_results', None)
