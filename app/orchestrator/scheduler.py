import cloudpickle as cpickle
import asyncio
from collections.abc import Callable
import dspy
from app.settings import settings
from dspy import Example
from dspy.teleprompt.mipro_optimizer_v2 import MIPROv2
from dspy.evaluate.metrics import answer_exact_match
from app.orchestrator.graph_recruiter import ResumeEvaluator
from supabase import create_client, Client
from app.settings import settings
import datetime
import logfire

# ------------------- Supabase Connection ------------------- #
SUPABASE_URL = settings.SUPABASE_URL
SUPABASE_KEY = settings.SUPABASE_API_KEY

supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# ------------------- Fetch Data from Supabase ------------------- #
def fetch_training_data():
    logfire.info("fetch_training_data_start", details={"message": "Fetching training data "})
    response = supabase.table("evaluation_results").select("*").eq("jury_flag", True).execute()
    logfire.info("fetch_training_data_processing", details={"message": "Preparing training examples from response data"})
    training_examples = []
    for item in response.data:
        if not item.get("evaluation_result"):
            continue
        try:
            example = Example(
                question=(item["resume_text"],item["job_description"]),
                answer=item["evaluation_result"]
            ).with_inputs('question')
            training_examples.append(example)
        except Exception as e:
            logfire.warning("fetch_training_data_parse_error", details={"error": str(e), "item_id": item.get("id")})
            continue
    logfire.info("fetch_training_data_success", details={"count": len(training_examples)})
    logfire.info("fetch_training_data_end", details={"message": "Data fetching function finished"})
    return training_examples

# ------------------- DSPy Optimizer using MIPROv2 ------------------- #
def train_and_save_model():
    logfire.info("train_model_start", details={"message": "Model optimization started"})
    print("Running optimization...")
    logfire.info("train_model_data_fetch", details={"message": "Calling function to fetch training data"})
    trainset=fetch_training_data()
    logfire.info("train_model_data_ready", details={"message": "Training data fetched", "count": len(trainset)})
    logfire.info("train_model_load_lm", details={"message": "Loading language model for optimization"})
    dspy.settings.configure(lm=dspy.LM(model='gpt-4-turbo', api_key=settings.OPENAI_API_KEY))
    logfire.info("train_model_load_optimizer", details={"message": "Loading optimizer (MIPROv2)"})
    evaluator = ResumeEvaluator()

    optimizer = MIPROv2(metric= answer_exact_match)
    logfire.info("train_model_optimizing", details={"message": "Compiling and optimizing the model"})
    optimized_model = optimizer.compile(evaluator, trainset=trainset,requires_permission_to_run=False)

    if hasattr(optimized_model, 'cot'):
        optimized_model.__dict__.pop('cot')
    
    logfire.info("train_model_save", details={"message": "Saving optimized model to disk"})
    
    with open("trained_model.pkl", "wb") as f:
        cpickle.dump(optimized_model, f)

    print("Optimized model saved as trained_model.pkl")
    logfire.info("train_model_end", details={"message": "Model optimization completed"})
# async def scheduled_training():
#     print("Starting resume evaluator training...")

#     training_data = fetch_training_data()
#     if not training_data:
#         print("No valid training data found in Supabase.")
#         return

#     train_and_save_model(trainset=training_data)
#     print("training completed.")

# For manual trigger
# if __name__ == "__main__":
#     asyncio.run(scheduled_training())
