import json
from openai import Async<PERSON>penAI
from app.settings import settings

llm = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)

async def get_llmanager_decision(similarity_score: float, evaluation_json: dict) -> tuple[str, str]:
    prompt = f"""
You are a hiring approval assistant.
Based on the resume evaluation and similarity score, decide:

- status: 'auto-approved' or 'needs-review'
- reason: short explanation

**Similarity Score**: {similarity_score}
**Evaluation JSON**:
{json.dumps(evaluation_json, indent=2)}

Respond in this format:
status: <auto-approved | needs-review>
reason: <short reason>
"""

    try:
        response = await llm.chat.completions.create(
            model="gpt-4-turbo",
            messages=[{"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ]
        )

        content = response.choices[0].message.content.strip()

        # Simple parser
        status = "needs-review"
        reason = "Could not parse reason"
        for line in content.splitlines():
            if line.lower().startswith("status:"):
                status = line.split(":", 1)[1].strip()
            elif line.lower().startswith("reason:"):
                reason = line.split(":", 1)[1].strip()

        return status, reason

    except Exception as e:
        return "needs-review", f"LLM Error: {str(e)}"
