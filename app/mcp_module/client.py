"""
Simple MCP Client - Direct Agent Gateway SSE Connection Only
"""
import asyncio
import httpx
import json
import logging
from typing import Dict, Any, Optional, AsyncGenerator

logger = logging.getLogger(__name__)

class AgentGatewaySSE:
    """Simple SSE client for Agent Gateway - Direct connection only"""
    
    def __init__(self, gateway_url: str = "http://127.0.0.1:3000"):
        self.gateway_url = gateway_url
        self.session_id: Optional[str] = None
        self.request_id = 0
        self.initialized = False
    
    def _get_next_id(self) -> int:
        """Get next request ID"""
        self.request_id += 1
        return self.request_id
    
    async def _get_session_id(self, force_new: bool = False) -> str:
        """Get session ID from Agent Gateway"""
        if self.session_id and not force_new:
            return self.session_id

        # Clear old session if getting new one
        if force_new:
            self.session_id = None

        async with httpx.AsyncClient(timeout=30.0) as client:
            logger.info("🚪 Getting session ID from Agent Gateway...")
            
            async with client.stream(
                "GET",
                f"{self.gateway_url}/sse",
                headers={
                    "Accept": "text/event-stream",
                    "Cache-Control": "no-cache"
                },
                timeout=10.0
            ) as response:
                
                if response.status_code != 200:
                    raise Exception(f"SSE connection failed: {response.status_code}")
                
                async for line in response.aiter_lines():
                    if line.startswith("data: ") and "sessionId=" in line:
                        data = line[6:]  # Remove "data: " prefix
                        self.session_id = data.split("sessionId=")[1].strip()
                        logger.info(f"✅ Got session ID: {self.session_id[:8]}...")
                        break
            
            if not self.session_id:
                raise Exception("No session ID received")
            
            # Initialize MCP session
            await self._initialize_mcp(client)
            return self.session_id
    
    async def _initialize_mcp(self, client: httpx.AsyncClient):
        """Initialize MCP session"""
        init_request = {
            "jsonrpc": "2.0",
            "id": self._get_next_id(),
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}, "prompts": {}},
                "clientInfo": {"name": "FastAPILangGraph", "version": "1.0.0"}
            }
        }
        
        response = await client.post(
            f"{self.gateway_url}/sse?sessionId={self.session_id}",
            json=init_request,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code not in [200, 202]:
            raise Exception(f"MCP init failed: {response.status_code}")

        # Small delay to ensure Agent Gateway processes the initialize
        import asyncio
        await asyncio.sleep(0.1)

        logger.info("🚪 MCP session initialized")

    async def _get_fresh_session_id(self, client: httpx.AsyncClient) -> str:
        """Get a fresh session ID from Agent Gateway"""
        logger.info("🚪 Getting session ID from Agent Gateway...")

        async with client.stream(
            "GET",
            f"{self.gateway_url}/sse",
            headers={"Accept": "text/event-stream"}
        ) as response:

            if response.status_code != 200:
                raise Exception(f"SSE connection failed: {response.status_code}")

            async for line in response.aiter_lines():
                if line.startswith("data: ") and "sessionId=" in line:
                    data = line[6:]  # Remove "data: " prefix
                    session_id = data.split("sessionId=")[1].strip()
                    self.session_id = session_id  # Store the session ID
                    logger.info(f"✅ Got session ID: {session_id[:8]}...")
                    return session_id

        raise Exception("No session ID received")

    async def _initialize_session(self, client: httpx.AsyncClient, session_id: str):
        """Initialize MCP session and handle redirects"""
        logger.info(f"🔄 Initializing MCP session for {session_id[:8]}...")

        init_request = {
            "jsonrpc": "2.0",
            "id": self._get_next_id(),
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}, "prompts": {}},
                "clientInfo": {"name": "FastAPILangGraph", "version": "1.0.0"}
            }
        }

        logger.info(f"📤 Sending initialize request: {init_request}")

        response = await client.post(
            f"{self.gateway_url}/sse?sessionId={session_id}",
            json=init_request,
            headers={"Content-Type": "application/json"}
        )

        logger.info(f"📥 Initialize response: {response.status_code}")

        if response.status_code not in [200, 202]:
            raise Exception(f"MCP init failed: {response.status_code}")

        # Listen for session redirect after initialization
        logger.info("👂 Listening for session redirect...")
        try:
            async with client.stream(
                "GET",
                f"{self.gateway_url}/sse?sessionId={session_id}",
                headers={"Accept": "text/event-stream"},
                timeout=10.0
            ) as sse_response:

                async for line in sse_response.aiter_lines():
                    logger.info(f"📥 Init SSE: {line}")

                    if line.startswith("data: ?sessionId="):
                        # Handle redirect - update session ID
                        new_session_id = line.split("sessionId=")[1].strip()
                        logger.info(f"🔄 Session redirected: {session_id[:8]} -> {new_session_id[:8]}")
                        self.session_id = new_session_id
                        break
                    elif line.strip() == "":
                        # Empty line, continue
                        continue
                    elif "event:" in line:
                        # Event line, continue
                        continue
                    else:
                        # Other data, break after a few lines
                        break

        except Exception as e:
            logger.warning(f"⚠️ Could not listen for redirect: {e}")

        # Small delay to ensure Agent Gateway processes the initialize
        import asyncio
        await asyncio.sleep(0.5)

        logger.info(f"✅ MCP session initialized for {self.session_id[:8] if self.session_id else session_id[:8]}")

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """Call a tool via Agent Gateway"""
        logger.info(f"🚀 AGENT GATEWAY CALL STARTED: {tool_name}")

        async with httpx.AsyncClient(timeout=60.0) as client:
            logger.info(f"🔧 Calling tool via Agent Gateway: {tool_name}")

            # Reuse existing session or get new one if needed
            if not self.session_id or not self.initialized:
                logger.info("🆕 Creating new session...")
                session_id = await self._get_fresh_session_id(client)
                await self._initialize_session(client, session_id)
                self.initialized = True
            else:
                logger.info(f"🔄 Reusing existing session: {self.session_id[:8]}...")

            # Agent Gateway prefixes tool names with MCP server name
            prefixed_tool_name = f"ces-mcp-server_{tool_name}"
            logger.info(f"🏷️ Using prefixed tool name: {prefixed_tool_name}")

            # Create tool request
            tool_request = {
                "jsonrpc": "2.0",
                "id": self._get_next_id(),
                "method": "tools/call",
                "params": {
                    "name": prefixed_tool_name,
                    "arguments": arguments
                }
            }

            # Send tool request using the current session ID (after any redirects)
            logger.info(f"📤 Sending tool request to session: {self.session_id[:8]}")
            logger.info(f"🔍 About to make POST request to: {self.gateway_url}/sse?sessionId={self.session_id}")

            try:
                logger.info(f"🔍 Making POST request...")
                response = await client.post(
                    f"{self.gateway_url}/sse?sessionId={self.session_id}",
                    json=tool_request,
                    headers={"Content-Type": "application/json"}
                )
                logger.info(f"🔍 POST request completed successfully, status: {response.status_code}")
                logger.info(f"🔍 Response object type: {type(response)}")
            except Exception as e:
                logger.error(f"❌ HTTP request failed with exception: {e}")
                logger.error(f"❌ Exception type: {type(e)}")
                logger.error(f"❌ Exception string: '{str(e)}'")
                logger.error(f"❌ Exception repr: {repr(e)}")

                # Check if this is a 404 error from httpx
                if "404" in str(e) or "Not Found" in str(e):
                    logger.warning(f"⚠️ Session {self.session_id[:8] if self.session_id else 'unknown'} returned 404 in exception, creating fresh session...")
                    # Reset session and try once more
                    self.session_id = None
                    self.initialized = False
                    # Retry with fresh session
                    await self._ensure_initialized()

                    prefixed_tool_name = f"ces-mcp-server_{tool_name}"
                    tool_request = {
                        "jsonrpc": "2.0",
                        "id": self._get_next_id(),
                        "method": "tools/call",
                        "params": {
                            "name": prefixed_tool_name,
                            "arguments": arguments
                        }
                    }

                    logger.info(f"🔄 Retrying with fresh session: {self.session_id[:8]}")
                    response = await client.post(
                        f"{self.gateway_url}/sse?sessionId={self.session_id}",
                        json=tool_request,
                        timeout=30.0
                    )

                    if response.status_code not in [200, 202]:
                        logger.error(f"❌ Fresh session also failed: {response.status_code}")
                        raise Exception(f"Tool request failed even with fresh session: {response.status_code}")
                else:
                    logger.error(f"❌ Non-404 exception, re-raising: {e}")
                    raise Exception(f"Tool request failed: {e}")

            logger.info(f"📥 Tool request response: {response.status_code}")
            logger.info(f"📥 Response status code type: {type(response.status_code)}")
            logger.info(f"📥 Response status code repr: {repr(response.status_code)}")
            if response.status_code not in [200, 202]:
                logger.info(f"🔍 Status code check: {response.status_code} == 404? {response.status_code == 404}")
                logger.info(f"🔍 Status code check: {response.status_code} == 404 (int)? {response.status_code == 404}")
                logger.info(f"🔍 Status code check: str({response.status_code}) == '404'? {str(response.status_code) == '404'}")
                # If 404, try with a fresh session
                if response.status_code == 404:
                    session_display = self.session_id[:8] if self.session_id else "unknown"
                    logger.warning(f"⚠️ Session {session_display} returned 404, creating fresh session...")
                    # Reset session and try once more
                    self.session_id = None
                    self.initialized = False

                    # Create fresh session
                    fresh_session_id = await self._get_fresh_session_id(client)
                    await self._initialize_session(client, fresh_session_id)

                    # Retry the tool request with fresh session
                    prefixed_tool_name = f"ces-mcp-server_{tool_name}"
                    tool_request = {
                        "jsonrpc": "2.0",
                        "id": self._get_next_id(),
                        "method": "tools/call",
                        "params": {
                            "name": prefixed_tool_name,
                            "arguments": arguments
                        }
                    }

                    logger.info(f"🔄 Retrying with fresh session: {self.session_id[:8]}")
                    response = await client.post(
                        f"{self.gateway_url}/sse?sessionId={self.session_id}",
                        json=tool_request,
                        timeout=30.0
                    )

                    if response.status_code not in [200, 202]:
                        logger.error(f"❌ Fresh session also failed: {response.status_code}")
                        raise Exception(f"Tool request failed even with fresh session: {response.status_code}")
                else:
                    # Reset session on other failures
                    logger.error(f"❌ Non-404 error: {response.status_code}")
                    self.session_id = None
                    self.initialized = False
                    raise Exception(f"Tool request failed: {response.status_code}")

            # Wait for Agent Gateway response and handle redirects
            import asyncio
            await asyncio.sleep(5.0)  # Give much more time for Agent Gateway to process

            # Try to get response, following redirects with better error handling
            current_session = self.session_id
            max_redirects = 10  # Increase redirect limit significantly
            redirect_count = 0
            response_timeout = 30.0  # Increase timeout significantly

            while redirect_count <= max_redirects:
                try:
                    logger.info(f"🔍 Listening on session {current_session[:8]}... (attempt {redirect_count + 1})")

                    async with client.stream(
                        "GET",
                        f"{self.gateway_url}/sse?sessionId={current_session}",
                        headers={"Accept": "text/event-stream", "Cache-Control": "no-cache"},
                        timeout=response_timeout
                    ) as sse_response:

                        line_count = 0
                        found_redirect = False

                        async for line in sse_response.aiter_lines():
                            line_count += 1
                            logger.info(f"📥 SSE Line {line_count}: {line}")

                            if line.startswith("data: ?sessionId="):
                                # Handle redirect
                                new_session_id = line.split("sessionId=")[1].strip()
                                logger.info(f"🔁 Agent Gateway redirect to: {new_session_id[:8]}...")
                                current_session = new_session_id
                                found_redirect = True
                                break  # Break to start new session

                            elif line.startswith("data: "):
                                data = line[6:].strip()
                                logger.info(f"🔍 Processing SSE data: {data[:100]}...")  # Log first 100 chars

                                if data not in ["ping", "[DONE]", ""]:
                                    try:
                                        response_data = json.loads(data)
                                        logger.info(f"📋 Parsed JSON: {response_data}")

                                        if "result" in response_data:
                                            logger.info("✅ Tool response received via Agent Gateway")
                                            result = response_data["result"]
                                            if isinstance(result, dict) and "content" in result:
                                                content = result["content"]
                                                if isinstance(content, list) and len(content) > 0:
                                                    return content[0].get("text", str(content[0]))
                                                return str(content)
                                            return str(result)
                                        elif "error" in response_data:
                                            logger.error(f"Agent Gateway error: {response_data['error']}")
                                            raise Exception(f"Agent Gateway error: {response_data['error']}")
                                        else:
                                            logger.info(f"📄 Other JSON response: {response_data}")
                                    except json.JSONDecodeError as e:
                                        logger.warning(f"Could not parse JSON: {data[:50]}... Error: {e}")
                                        continue

                            if line_count > 50:  # Reasonable limit per session
                                logger.warning("Reached max lines for this session")
                                break

                        if found_redirect:
                            redirect_count += 1
                            logger.info(f"Following redirect {redirect_count}/{max_redirects}")
                            continue
                        else:
                            # No redirect found, no response - break out
                            break

                except Exception as e:
                    logger.error(f"Session {current_session[:8]} failed: {e}")
                    break

            # If we get here, Agent Gateway didn't provide a response
            logger.error(f"❌ AGENT GATEWAY FAILED: {tool_name} after {redirect_count} redirects")

            # Try one more time with a fresh session as final attempt
            logger.info("🔄 Final attempt: Creating fresh session for Agent Gateway...")
            try:
                fresh_session_id = await self._get_fresh_session_id(client)
                await self._initialize_session(client, fresh_session_id)

                # Send tool request to fresh session (use self.session_id which has the redirected session)
                prefixed_tool_name = f"ces-mcp-server_{tool_name}"
                tool_request = {
                    "jsonrpc": "2.0",
                    "id": self._get_next_id(),
                    "method": "tools/call",
                    "params": {
                        "name": prefixed_tool_name,
                        "arguments": arguments
                    }
                }

                logger.info(f"📤 Final attempt tool request to session: {self.session_id[:8] if self.session_id else fresh_session_id[:8]}")
                response = await client.post(
                    f"{self.gateway_url}/sse?sessionId={self.session_id}",
                    json=tool_request,
                    headers={"Content-Type": "application/json"},
                    timeout=10.0
                )

                if response.status_code in [200, 202]:
                    # Try to get response quickly
                    await asyncio.sleep(3.0)

                    async with client.stream(
                        "GET",
                        f"{self.gateway_url}/sse?sessionId={self.session_id}",
                        headers={"Accept": "text/event-stream"},
                        timeout=10.0
                    ) as final_response:

                        line_count = 0
                        async for line in final_response.aiter_lines():
                            line_count += 1
                            logger.info(f"📥 Final SSE Line {line_count}: {line}")

                            if line.startswith("data: ") and not line.startswith("data: ?sessionId="):
                                data = line[6:].strip()
                                if data not in ["ping", "[DONE]", ""]:
                                    try:
                                        response_data = json.loads(data)
                                        if "result" in response_data:
                                            logger.info("✅ Final attempt SUCCESS via Agent Gateway")
                                            result = response_data["result"]
                                            if isinstance(result, dict) and "content" in result:
                                                content = result["content"]
                                                if isinstance(content, list) and len(content) > 0:
                                                    return content[0].get("text", str(content[0]))
                                                return str(content)
                                            return str(result)
                                    except json.JSONDecodeError:
                                        continue

                            if line_count > 20:  # Limit for final attempt
                                break

            except Exception as e:
                logger.warning(f"Final Agent Gateway attempt failed: {e}")

            # Final fallback - raise exception to trigger local fallback
            raise Exception(f"Agent Gateway did not provide response for tool: {tool_name} after all attempts")


    
    async def call_prompt(self, prompt_name: str, arguments: Dict[str, Any]) -> str:
        """Call a prompt via Agent Gateway"""
        async with httpx.AsyncClient(timeout=60.0) as client:
            logger.info(f"🎯 Calling prompt via Agent Gateway: {prompt_name}")

            # Get fresh session ID
            session_id = await self._get_fresh_session_id(client)

            # Initialize the session
            await self._initialize_session(client, session_id)

            # Create prompt request
            prompt_request = {
                "jsonrpc": "2.0",
                "id": self._get_next_id(),
                "method": "prompts/get",
                "params": {
                    "name": prompt_name,
                    "arguments": arguments
                }
            }

            # Send prompt request
            response = await client.post(
                f"{self.gateway_url}/sse?sessionId={session_id}",
                json=prompt_request,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code not in [200, 202]:
                raise Exception(f"Prompt request failed: {response.status_code}")

            # Simple approach: Wait a bit then check for response
            import asyncio
            await asyncio.sleep(1.0)

            # Try to get response
            try:
                async with client.stream(
                    "GET",
                    f"{self.gateway_url}/sse?sessionId={session_id}",
                    headers={"Accept": "text/event-stream"},
                    timeout=10.0
                ) as sse_response:

                    line_count = 0
                    async for line in sse_response.aiter_lines():
                        line_count += 1

                        if line.startswith("data: ") and not line.startswith("data: ?sessionId="):
                            data = line[6:]
                            if data.strip() not in ["ping", "[DONE]", ""]:
                                try:
                                    response_data = json.loads(data)
                                    if "result" in response_data:
                                        logger.info("✅ Prompt response received via Agent Gateway")
                                        result = response_data["result"]
                                        if isinstance(result, dict) and "messages" in result:
                                            messages = result["messages"]
                                            if isinstance(messages, list) and len(messages) > 0:
                                                content = messages[0].get("content", {})
                                                if isinstance(content, dict):
                                                    return content.get("text", str(content))
                                                return str(content)
                                        return str(result)
                                except json.JSONDecodeError:
                                    continue

                        if line_count > 20:
                            break
            except:
                pass

            # Fallback for intent classification
            if prompt_name == "classify_hr_query_intent":
                query = arguments.get("query", "").lower()
                if any(word in query for word in ["resume", "cv", "job", "description", "similarity"]):
                    return "resume_vs_jd"
                elif any(word in query for word in ["candidate", "evaluate", "assessment"]):
                    return "portal_candidate_eval"
                else:
                    return "general_hr_query"

            return "general_hr_query"
            prompt_request = {
                "jsonrpc": "2.0",
                "id": self._get_next_id(),
                "method": "prompts/get",
                "params": {
                    "name": prompt_name,
                    "arguments": arguments
                }
            }
            
            # Send prompt request
            response = await client.post(
                f"{self.gateway_url}/sse?sessionId={session_id}",
                json=prompt_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code not in [200, 202]:
                raise Exception(f"Prompt request failed: {response.status_code}")
            
            # Listen for response with timeout
            async with client.stream(
                "GET",
                f"{self.gateway_url}/sse?sessionId={session_id}",
                headers={"Accept": "text/event-stream"},
                timeout=10.0  # Reduced timeout
            ) as sse_response:

                line_count = 0
                async for line in sse_response.aiter_lines():
                    line_count += 1

                    if line.startswith("data: "):
                        data = line[6:]

                        # Skip ping messages
                        if data.strip() in ["ping", "[DONE]"]:
                            continue

                        try:
                            response_data = json.loads(data)

                            if "id" in response_data and response_data["id"] == prompt_request["id"]:
                                logger.info("✅ Prompt response received via Agent Gateway")

                                if "result" in response_data:
                                    result = response_data["result"]
                                    if isinstance(result, dict) and "messages" in result:
                                        messages = result["messages"]
                                        if isinstance(messages, list) and len(messages) > 0:
                                            content = messages[0].get("content", {})
                                            if isinstance(content, dict):
                                                return content.get("text", str(content))
                                            return str(content)
                                    return str(result)
                                elif "error" in response_data:
                                    raise Exception(f"Prompt error: {response_data['error']}")

                        except json.JSONDecodeError:
                            continue

                    # Prevent infinite waiting
                    if line_count > 20:
                        break

                raise Exception("No prompt response received")

# Global instance
gateway = AgentGatewaySSE()

# Simple wrapper functions for your existing code
async def classify_intent(query: str) -> str:
    """Classify intent using Agent Gateway"""
    result = await gateway.call_prompt("classify_hr_query_intent", {"query": query})
    intent = result.strip().lower()
    valid_intents = {"resume_vs_jd", "portal_candidate_eval", "general_hr_query"}
    return intent if intent in valid_intents else "general_hr_query"

async def process_hr_query(query: str) -> str:
    """Process HR query using OpenAI directly (to avoid infinite loop)"""
    try:
        from openai import AsyncOpenAI
        from app.settings import settings

        client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        response = await client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful HR assistant with expertise in recruitment, employee management, and HR best practices."},
                {"role": "user", "content": query}
            ],
            temperature=0.7,
            max_tokens=800
        )
        return response.choices[0].message.content
    except Exception as e:
        return f"I apologize, but I encountered an error while processing your query: {str(e)}"

async def calculate_resume_similarity(resume_text: str, job_description: str) -> Dict[str, Any]:
    """Calculate resume similarity using Agent Gateway"""
    result = await gateway.call_tool("calculate_resume_similarity", {
        "resume_text": resume_text,
        "job_description": job_description
    })
    return json.loads(result) if isinstance(result, str) else result

async def evaluate_resume_detailed(resume_text: str, job_description: str) -> Dict[str, Any]:
    """Evaluate resume in detail using Agent Gateway"""
    result = await gateway.call_tool("evaluate_resume_detailed", {
        "resume_text": resume_text,
        "job_description": job_description
    })
    return json.loads(result) if isinstance(result, str) else result

async def generate_hiring_decision(similarity_score: float, evaluation_json: Dict[str, Any]) -> str:
    """Generate hiring decision using Agent Gateway with robust fallback"""
    logger.info(f"🎯 DECISION GENERATION: similarity_score={similarity_score}, evaluation_json={evaluation_json}")

    try:
        result = await gateway.call_tool("generate_hiring_decision", {
            "similarity_score": similarity_score,
            "evaluation_json": evaluation_json
        })
        logger.info(f"✅ AGENT GATEWAY DECISION SUCCESS: {result}")
        return json.loads(result) if isinstance(result, str) else result
    except Exception as e:
        logger.warning(f"⚠️ Agent Gateway decision failed: {e}")

        # Enhanced fallback decision logic
        overall_score = evaluation_json.get("overall_score", 75) if evaluation_json else 75
        skills_score = evaluation_json.get("skills_match", {}).get("score", 75) if evaluation_json else 75

        # Combine similarity and evaluation scores for better decision
        combined_score = (similarity_score * 100 + overall_score + skills_score) / 3

        if combined_score >= 85:
            fallback_result = {
                "decision": "RECOMMEND",
                "reason": f"Strong candidate - Combined score: {combined_score:.1f}% (similarity: {similarity_score*100:.1f}%, evaluation: {overall_score}%) [LOCAL FALLBACK]"
            }
        elif combined_score >= 70:
            fallback_result = {
                "decision": "CONSIDER",
                "reason": f"Good candidate - Combined score: {combined_score:.1f}% (similarity: {similarity_score*100:.1f}%, evaluation: {overall_score}%) [LOCAL FALLBACK]"
            }
        else:
            fallback_result = {
                "decision": "REJECT",
                "reason": f"Insufficient match - Combined score: {combined_score:.1f}% (similarity: {similarity_score*100:.1f}%, evaluation: {overall_score}%) [LOCAL FALLBACK]"
            }

        logger.info(f"🔄 LOCAL FALLBACK DECISION: {fallback_result}")
        return fallback_result
