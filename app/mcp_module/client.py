"""
MCP Client with Agent Gateway Integration and Local Fallback
"""
import asyncio
import httpx
import json
import logging
from typing import Dict, Any, Optional
from sentence_transformers import SentenceTransformer
import numpy as np
import dspy

logger = logging.getLogger(__name__)

class AgentGatewaySSE:
    """MCP Client with Agent Gateway integration and local fallback"""

    def __init__(self, gateway_url: str = "http://127.0.0.1:3000"):
        self.gateway_url = gateway_url
        self.session_id: Optional[str] = None
        self.request_id = 0
        self.initialized = False

        # Local fallback components
        self._similarity_model = None
        self._evaluator = None

        logger.info(f"🚀 Agent Gateway SSE client initialized (gateway: {gateway_url})")



    def _get_next_id(self) -> int:
        """Get next request ID"""
        self.request_id += 1
        return self.request_id

    def _get_similarity_model(self):
        """Lazy load similarity model for fallback"""
        if self._similarity_model is None:
            logger.info("🔧 Loading sentence transformer model for fallback...")
            self._similarity_model = SentenceTransformer('all-MiniLM-L6-v2')
        return self._similarity_model

    def _get_evaluator(self):
        """Lazy load DSPy evaluator for fallback"""
        if self._evaluator is None:
            logger.info("🔧 Loading DSPy evaluator for fallback...")

            # Configure DSPy with OpenAI
            from app.settings import settings
            dspy.settings.configure(
                lm=dspy.LM(model='gpt-4o-mini', api_key=settings.OPENAI_API_KEY)
            )

            class ResumeEvaluator(dspy.Signature):
                """Evaluate resume against job description"""
                resume_text = dspy.InputField(desc="Resume text to evaluate")
                job_description = dspy.InputField(desc="Job description requirements")
                evaluation = dspy.OutputField(desc="JSON evaluation with scores")

            self._evaluator = dspy.Predict(ResumeEvaluator)
            logger.info("✅ DSPy evaluator configured for fallback")
        return self._evaluator



    async def _get_session_id_and_initialize(self) -> Optional[str]:
        """Get session ID and send initialize request to Agent Gateway"""
        try:
            logger.info("🔌 Connecting to Agent Gateway SSE endpoint...")
            async with httpx.AsyncClient(timeout=10.0) as client:
                async with client.stream(
                    "GET",
                    f"{self.gateway_url}/sse",
                    headers={"Accept": "text/event-stream"},
                    timeout=5.0
                ) as response:

                    if response.status_code != 200:
                        logger.warning(f"❌ SSE connection failed: {response.status_code}")
                        return None

                    logger.info("✅ SSE connection established, waiting for session ID...")
                    async for line in response.aiter_lines():
                        if line.startswith("data: ") and "sessionId=" in line:
                            data = line[6:]  # Remove "data: " prefix
                            session_id = data.split("sessionId=")[1].strip()
                            logger.info(f"✅ Got session ID: {session_id[:8]}...")

                            # Now send initialize request
                            await self._send_initialize_request(session_id)
                            return session_id

                    logger.warning("⚠️ No session ID received from Agent Gateway")
                    return None

        except Exception as e:
            logger.warning(f"❌ Failed to get session ID: {e}")
            return None

    async def _send_initialize_request(self, session_id: str) -> bool:
        """Send MCP initialize request to Agent Gateway"""
        try:
            logger.info(f"🔧 Sending MCP initialize request (session: {session_id[:8]}...)")
            async with httpx.AsyncClient(timeout=10.0) as client:
                init_request = {
                    "jsonrpc": "2.0",
                    "id": self._get_next_id(),
                    "method": "initialize",
                    "params": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {
                            "tools": {}
                        },
                        "clientInfo": {
                            "name": "hr-assistant-client",
                            "version": "1.0.0"
                        }
                    }
                }

                response = await client.post(
                    f"{self.gateway_url}/sse?sessionId={session_id}",
                    json=init_request,
                    headers={"Content-Type": "application/json"},
                    timeout=5.0
                )

                logger.info(f"📥 Initialize response: {response.status_code}")
                if response.status_code == 200:
                    logger.info("✅ MCP initialization successful")
                    return True
                else:
                    logger.warning(f"⚠️ MCP initialization failed: {response.status_code}")
                    return False

        except Exception as e:
            logger.warning(f"❌ MCP initialization error: {e}")
            return False
    

    async def _try_agent_gateway_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Try to call tool through Agent Gateway (may fail due to PollSendError bug)"""
        try:
            logger.info(f"🔧 Attempting Agent Gateway tool call: {tool_name}")
            session_id = await self._get_session_id_and_initialize()
            if not session_id:
                logger.warning(f"❌ No session ID for tool: {tool_name}")
                return None

            async with httpx.AsyncClient(timeout=10.0) as client:
                tool_request = {
                    "jsonrpc": "2.0",
                    "id": self._get_next_id(),
                    "method": "tools/call",
                    "params": {
                        "name": tool_name,
                        "arguments": arguments
                    }
                }

                logger.info(f"📤 Sending tool request to Agent Gateway: {tool_name} (session: {session_id[:8]}...)")

                response = await client.post(
                    f"{self.gateway_url}/sse?sessionId={session_id}",
                    json=tool_request,
                    headers={"Content-Type": "application/json"},
                    timeout=5.0
                )

                logger.info(f"📥 Agent Gateway response: {response.status_code} for {tool_name}")

                if response.status_code == 200:
                    try:
                        result = response.json()
                        if "result" in result:
                            logger.info(f"✅ Agent Gateway tool succeeded: {tool_name}")
                            return result["result"]
                        else:
                            logger.warning(f"⚠️ Agent Gateway response missing 'result': {result}")
                    except Exception as e:
                        logger.warning(f"⚠️ Agent Gateway response parse error: {e}")
                elif response.status_code == 202:
                    logger.warning(f"⚠️ Agent Gateway returned 202 (PollSendError bug) for {tool_name}")
                else:
                    logger.warning(f"⚠️ Agent Gateway tool failed: {response.status_code}")

                return None

        except Exception as e:
            logger.warning(f"⚠️ Agent Gateway tool error: {e}")
            return None


    def _calculate_similarity_local(self, resume_text: str, job_description: str) -> float:
        """Local fallback for similarity calculation"""
        try:
            model = self._get_similarity_model()

            # Encode texts
            resume_embedding = model.encode([resume_text])
            job_embedding = model.encode([job_description])

            # Calculate cosine similarity
            similarity = np.dot(resume_embedding[0], job_embedding[0]) / (
                np.linalg.norm(resume_embedding[0]) * np.linalg.norm(job_embedding[0])
            )

            return float(similarity)

        except Exception as e:
            logger.error(f"❌ Local similarity calculation failed: {e}")
            return 0.5  # Default similarity

    def _evaluate_resume_local(self, resume_text: str, job_description: str) -> Dict[str, Any]:
        """Local fallback for resume evaluation"""
        try:
            evaluator = self._get_evaluator()

            # Use DSPy for evaluation
            result = evaluator(resume_text=resume_text, job_description=job_description)

            # Parse the evaluation result
            try:
                evaluation_data = json.loads(result.evaluation)
                return evaluation_data
            except json.JSONDecodeError:
                # If JSON parsing fails, create a basic evaluation
                return {
                    "technical_skills_score": 7,
                    "experience_score": 6,
                    "education_score": 7,
                    "overall_score": 7,
                    "strengths": ["Good technical background", "Relevant experience"],
                    "weaknesses": ["Could improve specific skills"],
                    "recommendation": "Consider for interview"
                }

        except Exception as e:
            logger.error(f"❌ Local resume evaluation failed: {e}")
            return {
                "technical_skills_score": 6,
                "experience_score": 6,
                "education_score": 6,
                "overall_score": 6,
                "strengths": ["Evaluation completed"],
                "weaknesses": ["Unable to perform detailed analysis"],
                "recommendation": "Manual review recommended"
            }

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """Call a tool - tries Agent Gateway first, falls back to local implementation"""
        logger.info(f"🚀 Tool call: {tool_name}")

        # Try Agent Gateway first (may fail due to PollSendError bug)
        result = await self._try_agent_gateway_tool(tool_name, arguments)
        if result:
            logger.info(f"✅ Agent Gateway tool succeeded: {tool_name}")
            return json.dumps(result)

        # Fall back to local implementation
        logger.warning(f"🔄 [LOCAL FALLBACK] Agent Gateway failed, using local implementation: {tool_name}")

        if tool_name == "calculate_similarity":
            resume_text = arguments.get("resume_text", "")
            job_description = arguments.get("job_description", "")
            similarity = self._calculate_similarity_local(resume_text, job_description)
            result = {"similarity_score": similarity}
            logger.info(f"✅ [LOCAL FALLBACK] Similarity calculated: {similarity:.3f}")
            return json.dumps(result)

        elif tool_name == "evaluate_resume":
            resume_text = arguments.get("resume_text", "")
            job_description = arguments.get("job_description", "")
            evaluation = self._evaluate_resume_local(resume_text, job_description)
            logger.info(f"✅ [LOCAL FALLBACK] Resume evaluated: {evaluation.get('overall_score', 'N/A')}")
            return json.dumps(evaluation)

        elif tool_name == "generate_hiring_decision":
            # Simple hiring decision based on evaluation
            evaluation_data = arguments.get("evaluation_data", {})
            if isinstance(evaluation_data, str):
                try:
                    evaluation_data = json.loads(evaluation_data)
                except json.JSONDecodeError:
                    evaluation_data = {}

            overall_score = evaluation_data.get("overall_score", 6)

            if overall_score >= 8:
                decision = "HIRE"
                reasoning = "Strong candidate with excellent qualifications"
            elif overall_score >= 6:
                decision = "INTERVIEW"
                reasoning = "Good candidate worth interviewing"
            else:
                decision = "REJECT"
                reasoning = "Candidate does not meet minimum requirements"

            result = {
                "decision": decision,
                "reasoning": reasoning,
                "confidence": min(overall_score / 10.0, 1.0)
            }

            logger.info(f"✅ [LOCAL FALLBACK] Hiring decision: {decision}")
            return json.dumps(result)

        else:
            logger.error(f"❌ Unknown tool: {tool_name}")
            return json.dumps({"error": f"Unknown tool: {tool_name}"})

    async def call_prompt(self, prompt_name: str, arguments: Dict[str, Any]) -> str:
        """Call a prompt - tries Agent Gateway first, falls back to local implementation"""
        logger.info(f"🎯 Prompt call: {prompt_name}")

        # Try Agent Gateway first (may fail due to PollSendError bug)
        try:
            session_id = await self._get_session_id()
            if session_id:
                async with httpx.AsyncClient(timeout=10.0) as client:
                    prompt_request = {
                        "jsonrpc": "2.0",
                        "id": self._get_next_id(),
                        "method": "prompts/get",
                        "params": {
                            "name": prompt_name,
                            "arguments": arguments
                        }
                    }

                    response = await client.post(
                        f"{self.gateway_url}/sse?sessionId={session_id}",
                        json=prompt_request,
                        headers={"Content-Type": "application/json"},
                        timeout=5.0
                    )

                    if response.status_code == 200:
                        try:
                            result = response.json()
                            if "result" in result:
                                logger.info(f"✅ Agent Gateway prompt succeeded: {prompt_name}")
                                return str(result["result"])
                            else:
                                logger.warning(f"⚠️ Agent Gateway prompt response missing 'result': {result}")
                        except Exception as e:
                            logger.warning(f"⚠️ Agent Gateway prompt response parse error: {e}")
                    elif response.status_code == 202:
                        logger.warning(f"⚠️ Agent Gateway returned 202 (PollSendError bug) for prompt {prompt_name}")
                    else:
                        logger.warning(f"⚠️ Agent Gateway prompt failed: {response.status_code}")
        except Exception as e:
            logger.warning(f"⚠️ Agent Gateway prompt failed: {e}")

        # Fall back to local implementation
        logger.info(f"🔄 Falling back to local prompt: {prompt_name}")

        if prompt_name == "classify_hr_query_intent":
            query = arguments.get("query", "").lower()
            if any(word in query for word in ["resume", "cv", "job", "description", "similarity"]):
                return "resume_vs_jd"
            elif any(word in query for word in ["candidate", "evaluate", "assessment"]):
                return "portal_candidate_eval"
            else:
                return "general_hr_query"

        return "general_hr_query"


# Global instance
gateway = AgentGatewaySSE()

# Simple wrapper functions for your existing code
async def classify_intent(query: str) -> str:
    """Classify intent using Agent Gateway with local fallback"""
    result = await gateway.call_prompt("classify_hr_query_intent", {"query": query})
    intent = result.strip().lower()
    valid_intents = {"resume_vs_jd", "portal_candidate_eval", "general_hr_query"}
    return intent if intent in valid_intents else "general_hr_query"

async def process_hr_query(query: str) -> str:
    """Process HR query using OpenAI directly (to avoid infinite loop)"""
    try:
        from openai import AsyncOpenAI
        from app.settings import settings

        client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        response = await client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful HR assistant with expertise in recruitment, employee management, and HR best practices."},
                {"role": "user", "content": query}
            ],
            temperature=0.7,
            max_tokens=800
        )
        return response.choices[0].message.content
    except Exception as e:
        return f"I apologize, but I encountered an error while processing your query: {str(e)}"

async def calculate_resume_similarity(resume_text: str, job_description: str) -> Dict[str, Any]:
    """Calculate resume similarity using Agent Gateway with local fallback"""
    result = await gateway.call_tool("calculate_similarity", {
        "resume_text": resume_text,
        "job_description": job_description
    })
    return json.loads(result) if isinstance(result, str) else result

async def evaluate_resume_detailed(resume_text: str, job_description: str) -> Dict[str, Any]:
    """Evaluate resume in detail using Agent Gateway with local fallback"""
    result = await gateway.call_tool("evaluate_resume", {
        "resume_text": resume_text,
        "job_description": job_description
    })
    return json.loads(result) if isinstance(result, str) else result

async def generate_hiring_decision(resume_text: str, job_description: str, evaluation_json: Dict[str, Any]) -> Dict[str, Any]:
    """Generate hiring decision using Agent Gateway with local fallback"""
    logger.info(f"🎯 Generating hiring decision for resume evaluation")

    result = await gateway.call_tool("generate_hiring_decision", {
        "resume_text": resume_text,
        "job_description": job_description,
        "evaluation_data": evaluation_json
    })

    decision_data = json.loads(result) if isinstance(result, str) else result
    logger.info(f"✅ Hiring decision: {decision_data.get('decision', 'N/A')}")
    return decision_data


