"""
Simple MCP Client - Direct Agent Gateway SSE Connection Only
"""
import asyncio
import httpx
import json
import logging
from typing import Dict, Any, Optional, AsyncGenerator

logger = logging.getLogger(__name__)

class AgentGatewaySSE:
    """Simple SSE client for Agent Gateway - Direct connection only"""
    
    def __init__(self, gateway_url: str = "http://127.0.0.1:3000"):
        self.gateway_url = gateway_url
        self.session_id: Optional[str] = None
        self.request_id = 0
    
    def _get_next_id(self) -> int:
        """Get next request ID"""
        self.request_id += 1
        return self.request_id
    
    async def _get_session_id(self, force_new: bool = False) -> str:
        """Get session ID from Agent Gateway"""
        if self.session_id and not force_new:
            return self.session_id

        # Clear old session if getting new one
        if force_new:
            self.session_id = None

        async with httpx.AsyncClient(timeout=30.0) as client:
            logger.info("🚪 Getting session ID from Agent Gateway...")
            
            async with client.stream(
                "GET",
                f"{self.gateway_url}/sse",
                headers={
                    "Accept": "text/event-stream",
                    "Cache-Control": "no-cache"
                },
                timeout=10.0
            ) as response:
                
                if response.status_code != 200:
                    raise Exception(f"SSE connection failed: {response.status_code}")
                
                async for line in response.aiter_lines():
                    if line.startswith("data: ") and "sessionId=" in line:
                        data = line[6:]  # Remove "data: " prefix
                        self.session_id = data.split("sessionId=")[1].strip()
                        logger.info(f"✅ Got session ID: {self.session_id[:8]}...")
                        break
            
            if not self.session_id:
                raise Exception("No session ID received")
            
            # Initialize MCP session
            await self._initialize_mcp(client)
            return self.session_id
    
    async def _initialize_mcp(self, client: httpx.AsyncClient):
        """Initialize MCP session"""
        init_request = {
            "jsonrpc": "2.0",
            "id": self._get_next_id(),
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}, "prompts": {}},
                "clientInfo": {"name": "FastAPILangGraph", "version": "1.0.0"}
            }
        }
        
        response = await client.post(
            f"{self.gateway_url}/sse?sessionId={self.session_id}",
            json=init_request,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code not in [200, 202]:
            raise Exception(f"MCP init failed: {response.status_code}")

        # Small delay to ensure Agent Gateway processes the initialize
        import asyncio
        await asyncio.sleep(0.1)

        logger.info("🚪 MCP session initialized")

    async def _get_fresh_session_id(self, client: httpx.AsyncClient) -> str:
        """Get a fresh session ID from Agent Gateway"""
        logger.info("🚪 Getting session ID from Agent Gateway...")

        async with client.stream(
            "GET",
            f"{self.gateway_url}/sse",
            headers={"Accept": "text/event-stream"}
        ) as response:

            if response.status_code != 200:
                raise Exception(f"SSE connection failed: {response.status_code}")

            async for line in response.aiter_lines():
                if line.startswith("data: ") and "sessionId=" in line:
                    data = line[6:]  # Remove "data: " prefix
                    session_id = data.split("sessionId=")[1].strip()
                    logger.info(f"✅ Got session ID: {session_id[:8]}...")
                    return session_id

        raise Exception("No session ID received")

    async def _initialize_session(self, client: httpx.AsyncClient, session_id: str):
        """Initialize MCP session"""
        logger.info(f"🔄 Initializing MCP session for {session_id[:8]}...")

        init_request = {
            "jsonrpc": "2.0",
            "id": self._get_next_id(),
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}, "prompts": {}},
                "clientInfo": {"name": "FastAPILangGraph", "version": "1.0.0"}
            }
        }

        logger.info(f"📤 Sending initialize request: {init_request}")

        response = await client.post(
            f"{self.gateway_url}/sse?sessionId={session_id}",
            json=init_request,
            headers={"Content-Type": "application/json"}
        )

        logger.info(f"📥 Initialize response: {response.status_code}")

        if response.status_code not in [200, 202]:
            raise Exception(f"MCP init failed: {response.status_code}")

        # Small delay to ensure Agent Gateway processes the initialize
        import asyncio
        await asyncio.sleep(0.5)  # Increased delay

        logger.info(f"✅ MCP session initialized for {session_id[:8]}")

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """Call a tool via Agent Gateway"""
        async with httpx.AsyncClient(timeout=60.0) as client:
            logger.info(f"🔧 Calling tool via Agent Gateway: {tool_name}")

            # Get fresh session ID
            session_id = await self._get_fresh_session_id(client)

            # Initialize the session
            await self._initialize_session(client, session_id)

            # Create tool request
            tool_request = {
                "jsonrpc": "2.0",
                "id": self._get_next_id(),
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }

            # Send tool request
            response = await client.post(
                f"{self.gateway_url}/sse?sessionId={session_id}",
                json=tool_request,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code not in [200, 202]:
                raise Exception(f"Tool request failed: {response.status_code}")

            # Wait for Agent Gateway response and handle redirects
            import asyncio
            await asyncio.sleep(1.0)  # Give time for processing

            # Try to get response, following redirects
            current_session = session_id
            max_redirects = 3
            redirect_count = 0

            while redirect_count <= max_redirects:
                try:
                    logger.info(f"🔍 Listening on session {current_session[:8]}... (attempt {redirect_count + 1})")

                    async with client.stream(
                        "GET",
                        f"{self.gateway_url}/sse?sessionId={current_session}",
                        headers={"Accept": "text/event-stream"},
                        timeout=15.0
                    ) as sse_response:

                        line_count = 0
                        found_redirect = False

                        async for line in sse_response.aiter_lines():
                            line_count += 1
                            logger.info(f"📥 SSE Line {line_count}: {line}")

                            if line.startswith("data: ?sessionId="):
                                # Handle redirect
                                new_session_id = line.split("sessionId=")[1].strip()
                                logger.info(f"🔁 Agent Gateway redirect to: {new_session_id[:8]}...")
                                current_session = new_session_id
                                found_redirect = True
                                break  # Break to start new session

                            elif line.startswith("data: "):
                                data = line[6:]
                                if data.strip() not in ["ping", "[DONE]", ""]:
                                    try:
                                        response_data = json.loads(data)
                                        if "result" in response_data:
                                            logger.info("✅ Tool response received via Agent Gateway")
                                            result = response_data["result"]
                                            if isinstance(result, dict) and "content" in result:
                                                content = result["content"]
                                                if isinstance(content, list) and len(content) > 0:
                                                    return content[0].get("text", str(content[0]))
                                                return str(content)
                                            return str(result)
                                        elif "error" in response_data:
                                            logger.error(f"Agent Gateway error: {response_data['error']}")
                                            raise Exception(f"Agent Gateway error: {response_data['error']}")
                                    except json.JSONDecodeError:
                                        logger.warning(f"Could not parse JSON: {data}")
                                        continue

                            if line_count > 50:  # Reasonable limit per session
                                logger.warning("Reached max lines for this session")
                                break

                        if found_redirect:
                            redirect_count += 1
                            logger.info(f"Following redirect {redirect_count}/{max_redirects}")
                            continue
                        else:
                            # No redirect found, no response - break out
                            break

                except Exception as e:
                    logger.error(f"Session {current_session[:8]} failed: {e}")
                    break

            # If we get here, Agent Gateway didn't provide a response
            raise Exception(f"Agent Gateway did not provide response for tool: {tool_name} after {redirect_count} redirects")


    
    async def call_prompt(self, prompt_name: str, arguments: Dict[str, Any]) -> str:
        """Call a prompt via Agent Gateway"""
        async with httpx.AsyncClient(timeout=60.0) as client:
            logger.info(f"🎯 Calling prompt via Agent Gateway: {prompt_name}")

            # Get fresh session ID
            session_id = await self._get_fresh_session_id(client)

            # Initialize the session
            await self._initialize_session(client, session_id)

            # Create prompt request
            prompt_request = {
                "jsonrpc": "2.0",
                "id": self._get_next_id(),
                "method": "prompts/get",
                "params": {
                    "name": prompt_name,
                    "arguments": arguments
                }
            }

            # Send prompt request
            response = await client.post(
                f"{self.gateway_url}/sse?sessionId={session_id}",
                json=prompt_request,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code not in [200, 202]:
                raise Exception(f"Prompt request failed: {response.status_code}")

            # Simple approach: Wait a bit then check for response
            import asyncio
            await asyncio.sleep(1.0)

            # Try to get response
            try:
                async with client.stream(
                    "GET",
                    f"{self.gateway_url}/sse?sessionId={session_id}",
                    headers={"Accept": "text/event-stream"},
                    timeout=10.0
                ) as sse_response:

                    line_count = 0
                    async for line in sse_response.aiter_lines():
                        line_count += 1

                        if line.startswith("data: ") and not line.startswith("data: ?sessionId="):
                            data = line[6:]
                            if data.strip() not in ["ping", "[DONE]", ""]:
                                try:
                                    response_data = json.loads(data)
                                    if "result" in response_data:
                                        logger.info("✅ Prompt response received via Agent Gateway")
                                        result = response_data["result"]
                                        if isinstance(result, dict) and "messages" in result:
                                            messages = result["messages"]
                                            if isinstance(messages, list) and len(messages) > 0:
                                                content = messages[0].get("content", {})
                                                if isinstance(content, dict):
                                                    return content.get("text", str(content))
                                                return str(content)
                                        return str(result)
                                except json.JSONDecodeError:
                                    continue

                        if line_count > 20:
                            break
            except:
                pass

            # Fallback for intent classification
            if prompt_name == "classify_hr_query_intent":
                query = arguments.get("query", "").lower()
                if any(word in query for word in ["resume", "cv", "job", "description", "similarity"]):
                    return "resume_vs_jd"
                elif any(word in query for word in ["candidate", "evaluate", "assessment"]):
                    return "portal_candidate_eval"
                else:
                    return "general_hr_query"

            return "general_hr_query"
            prompt_request = {
                "jsonrpc": "2.0",
                "id": self._get_next_id(),
                "method": "prompts/get",
                "params": {
                    "name": prompt_name,
                    "arguments": arguments
                }
            }
            
            # Send prompt request
            response = await client.post(
                f"{self.gateway_url}/sse?sessionId={session_id}",
                json=prompt_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code not in [200, 202]:
                raise Exception(f"Prompt request failed: {response.status_code}")
            
            # Listen for response with timeout
            async with client.stream(
                "GET",
                f"{self.gateway_url}/sse?sessionId={session_id}",
                headers={"Accept": "text/event-stream"},
                timeout=10.0  # Reduced timeout
            ) as sse_response:

                line_count = 0
                async for line in sse_response.aiter_lines():
                    line_count += 1

                    if line.startswith("data: "):
                        data = line[6:]

                        # Skip ping messages
                        if data.strip() in ["ping", "[DONE]"]:
                            continue

                        try:
                            response_data = json.loads(data)

                            if "id" in response_data and response_data["id"] == prompt_request["id"]:
                                logger.info("✅ Prompt response received via Agent Gateway")

                                if "result" in response_data:
                                    result = response_data["result"]
                                    if isinstance(result, dict) and "messages" in result:
                                        messages = result["messages"]
                                        if isinstance(messages, list) and len(messages) > 0:
                                            content = messages[0].get("content", {})
                                            if isinstance(content, dict):
                                                return content.get("text", str(content))
                                            return str(content)
                                    return str(result)
                                elif "error" in response_data:
                                    raise Exception(f"Prompt error: {response_data['error']}")

                        except json.JSONDecodeError:
                            continue

                    # Prevent infinite waiting
                    if line_count > 20:
                        break

                raise Exception("No prompt response received")

# Global instance
gateway = AgentGatewaySSE()

# Simple wrapper functions for your existing code
async def classify_intent(query: str) -> str:
    """Classify intent using Agent Gateway"""
    result = await gateway.call_prompt("classify_hr_query_intent", {"query": query})
    intent = result.strip().lower()
    valid_intents = {"resume_vs_jd", "portal_candidate_eval", "general_hr_query"}
    return intent if intent in valid_intents else "general_hr_query"

async def process_hr_query(query: str) -> str:
    """Process HR query using OpenAI directly (to avoid infinite loop)"""
    try:
        from openai import AsyncOpenAI
        from app.settings import settings

        client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        response = await client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful HR assistant with expertise in recruitment, employee management, and HR best practices."},
                {"role": "user", "content": query}
            ],
            temperature=0.7,
            max_tokens=800
        )
        return response.choices[0].message.content
    except Exception as e:
        return f"I apologize, but I encountered an error while processing your query: {str(e)}"

async def calculate_resume_similarity(resume_text: str, job_description: str) -> Dict[str, Any]:
    """Calculate resume similarity using Agent Gateway"""
    result = await gateway.call_tool("calculate_resume_similarity", {
        "resume_text": resume_text,
        "job_description": job_description
    })
    return json.loads(result) if isinstance(result, str) else result

async def evaluate_resume_detailed(resume_text: str, job_description: str) -> Dict[str, Any]:
    """Evaluate resume in detail using Agent Gateway"""
    result = await gateway.call_tool("evaluate_resume_detailed", {
        "resume_text": resume_text,
        "job_description": job_description
    })
    return json.loads(result) if isinstance(result, str) else result

async def generate_hiring_decision(similarity_score: float, evaluation_json: Dict[str, Any]) -> str:
    """Generate hiring decision using Agent Gateway"""
    result = await gateway.call_tool("generate_hiring_decision", {
        "similarity_score": similarity_score,
        "evaluation_json": evaluation_json
    })
    return json.loads(result) if isinstance(result, str) else result
