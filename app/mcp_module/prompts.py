import sys
import os

# project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# if project_root not in sys.path:
#     sys.path.insert(0, project_root)

try:
    from app.mcp_module.config import mcp
except ImportError:
    from fastmcp import FastMCP
    mcp = FastMCP("Recruiter MCP")

@mcp.prompt()
def classify_hr_query_intent(query: str) -> str:
    return f"""
    You are a classifier that returns EXACTLY one of: resume_vs_jd, portal_candidate_eval, general_hr_query.

    Rules:
    - resume_vs_jd: ONLY when the user is explicitly asking to compare a specific resume to a specific job description (they must provide both documents)
    - portal_candidate_eval: ONLY when evaluating candidates from a portal/website
    - general_hr_query: For ALL other HR questions, advice, best practices, general inquiries about hiring, etc.

    Most queries should be classified as "general_hr_query" unless they explicitly involve document comparison.

    Classify this query: {query}

    Return only the classification, nothing else.
    """
