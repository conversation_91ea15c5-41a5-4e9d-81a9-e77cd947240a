from fastmcp import FastMCP
from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse
from pydantic import BaseModel
import logging
import sys
import os
import json
import asyncio
from typing import Optional, Dict, Any, AsyncGenerator

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

mcp = FastMCP("Recruiter MCP")

# Ensure prompts and tools are registered
def register_mcp_components():
    """Register MCP tools and prompts"""
    try:
        # Try importing with app prefix first
        from app.mcp_module import prompts  # noqa: F401
        from app.mcp_module import tools  # noqa: F401
        logger.info("MCP components imported with app prefix")
    except ImportError:
        try:
            # Fallback for direct execution
            import prompts  # noqa: F401
            import tools  # noqa: F401
            logger.info("MCP components imported directly")
        except ImportError as e:
            logger.error(f"Failed to import MCP components: {e}")
            raise

# Register components
register_mcp_components()

# Pydantic models for HTTP API
class PromptRequest(BaseModel):
    name: str
    arguments: Dict[str, Any]

class ToolRequest(BaseModel):
    name: str
    arguments: Dict[str, Any]

class MCPResponse(BaseModel):
    content: Any
    error: Optional[str] = None

# Add HTTP endpoints to the FastMCP app
def setup_http_endpoints():
    """Setup HTTP endpoints for MCP tools and prompts"""
    # Create a new FastAPI app for HTTP endpoints
    from fastapi import FastAPI
    app = FastAPI(title="MCP HTTP Server", description="HTTP endpoints for MCP tools and prompts")

    # Store the app in mcp for later use
    mcp.http_app = app

    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return {"status": "healthy", "service": "MCP Server"}

    @app.post("/prompts/call", response_model=MCPResponse)
    async def call_prompt_http(request: PromptRequest):
        """HTTP endpoint for calling MCP prompts"""
        global query
        try:
            # Get the prompt from FastMCP
            if hasattr(mcp, '_prompts') and request.name in mcp._prompts:
                prompt_func = mcp._prompts[request.name]
                result = await prompt_func(**request.arguments)
                return MCPResponse(content=result)
            else:
                if request.name == "classify_hr_query_intent":
                    try:
                        from app.mcp_module.prompts import classify_hr_query_intent
                        query = request.arguments.get('query', '')

                        # Get the prompt template using FastMCP prompt function
                        prompt_template = classify_hr_query_intent.fn(query)

                        # Call OpenAI with the prompt
                        from openai import AsyncOpenAI
                        from app.settings import settings

                        client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
                        response = await client.chat.completions.create(
                            model="gpt-4o-mini",  # Faster model
                            messages=[{"role": "user", "content": prompt_template}],
                            temperature=0.1,
                            max_tokens=50
                        )

                        result = response.choices[0].message.content.strip().lower()
                        valid_intents = {"resume_vs_jd", "portal_candidate_eval", "general_hr_query"}
                        final_result = result if result in valid_intents else "general_hr_query"

                        logger.info(f"MCP prompt classification result: {final_result}")
                        return MCPResponse(content=final_result)

                    except Exception as e:
                        logger.error(f"MCP prompt classification failed: {e}")
                        # Simple fallback
                        query_lower = query.lower()
                        if "resume" in query_lower and ("job description" in query_lower or "jd" in query_lower):
                            result = "resume_vs_jd"
                        elif "portal" in query_lower or "candidate" in query_lower:
                            result = "portal_candidate_eval"
                        else:
                            result = "general_hr_query"
                        return MCPResponse(content=result)

                raise HTTPException(status_code=404, detail=f"Prompt '{request.name}' not found")

        except Exception as e:
            logger.error(f"Error calling prompt {request.name}: {e}")
            return MCPResponse(content="", error=str(e))

    @app.post("/tools/call", response_model=MCPResponse)
    async def call_tool_http(request: ToolRequest):
        """HTTP endpoint for calling MCP tools"""
        try:
            # Get the tool from FastMCP
            if hasattr(mcp, '_tools') and request.name in mcp._tools:
                tool_func = mcp._tools[request.name]
                result = await tool_func(**request.arguments)
                return MCPResponse(content=result)
            else:
                # Use actual MCP tools
                if request.name == "process_hr_query":
                    from app.mcp_module.tools import process_hr_query
                    result = await process_hr_query.fn(request.arguments.get("query", ""))
                    return MCPResponse(content=result)
                elif request.name == "calculate_resume_similarity":
                    from app.mcp_module.tools import calculate_resume_similarity
                    result = await calculate_resume_similarity.fn(
                        request.arguments.get("resume_text", ""),
                        request.arguments.get("job_description", "")
                    )
                    return MCPResponse(content=result)

                raise HTTPException(status_code=404, detail=f"Tool '{request.name}' not found")

        except Exception as e:
            logger.error(f"Error calling tool {request.name}: {e}")
            return MCPResponse(content={}, error=str(e))

    @app.post("/tools/call/stream")
    async def call_tool_streaming(request: ToolRequest):
        """SSE endpoint for streaming MCP tool responses"""
        async def generate_stream():
            try:
                # Send initial status
                yield {
                    "event": "data",
                    "data": json.dumps({"status": "processing", "tool": request.name})
                }

                if request.name == "process_hr_query":
                    from app.mcp_module.tools import process_hr_query
                    result = await process_hr_query.fn(request.arguments.get("query", ""))

                    # Stream the response in chunks
                    words = str(result).split()
                    total_chunks = (len(words) + 4) // 5  # Calculate total chunks

                    for i in range(0, len(words), 5):  # Send 5 words at a time
                        chunk = " ".join(words[i:i+5])
                        chunk_num = (i // 5) + 1

                        yield {
                            "event": "data",
                            "data": json.dumps({
                                "content": chunk + " ",
                                "chunk": chunk_num,
                                "total_chunks": total_chunks,
                                "status": "streaming"
                            })
                        }
                        await asyncio.sleep(0.03)  # Faster streaming

                elif request.name == "calculate_resume_similarity":
                    from app.mcp_module.tools import calculate_resume_similarity
                    result = await calculate_resume_similarity.fn(
                        request.arguments.get("resume_text", ""),
                        request.arguments.get("job_description", "")
                    )

                    # Stream similarity calculation steps
                    yield {
                        "event": "data",
                        "data": json.dumps({"status": "analyzing", "step": "processing resume"})
                    }
                    await asyncio.sleep(0.1)

                    yield {
                        "event": "data",
                        "data": json.dumps({"status": "analyzing", "step": "processing job description"})
                    }
                    await asyncio.sleep(0.1)

                    yield {
                        "event": "data",
                        "data": json.dumps({"status": "calculating", "step": "computing similarity"})
                    }
                    await asyncio.sleep(0.1)

                    yield {
                        "event": "data",
                        "data": json.dumps({"content": result, "status": "completed"})
                    }

                elif request.name == "evaluate_resume_detailed":
                    from app.mcp_module.tools import evaluate_resume_detailed
                    result = await evaluate_resume_detailed.fn(
                        request.arguments.get("resume_text", ""),
                        request.arguments.get("job_description", "")
                    )

                    # Stream evaluation steps
                    steps = ["skills analysis", "experience review", "qualification check", "final scoring"]
                    for i, step in enumerate(steps):
                        yield {
                            "event": "data",
                            "data": json.dumps({"status": "evaluating", "step": step, "progress": (i+1)/len(steps)*100})
                        }
                        await asyncio.sleep(0.2)

                    yield {
                        "event": "data",
                        "data": json.dumps({"content": result, "status": "completed"})
                    }

                else:
                    # Generic tool handling
                    yield {
                        "event": "data",
                        "data": json.dumps({"status": "executing", "tool": request.name})
                    }

                    # Try to find and execute the tool
                    for tool in mcp.list_tools():
                        if tool.name == request.name:
                            result = await tool.fn(**request.arguments)
                            yield {
                                "event": "data",
                                "data": json.dumps({"content": result, "status": "completed"})
                            }
                            break
                    else:
                        yield {
                            "event": "data",
                            "data": json.dumps({"error": f"Tool '{request.name}' not found", "status": "error"})
                        }

                # Send completion signal
                yield {
                    "event": "data",
                    "data": "[DONE]"
                }

            except Exception as e:
                logger.error(f"Error in streaming tool call {request.name}: {e}")
                yield {
                    "event": "error",
                    "data": json.dumps({"error": str(e)})
                }

        return EventSourceResponse(generate_stream())

    return app

# Setup HTTP endpoints
app = setup_http_endpoints()
mcp.http_app = app

# Global server instance for shutdown
_server_instance: Optional[object] = None

async def start_mcp_server(host: str = "0.0.0.0", port: int = 9000):
    """Start the MCP server"""
    global _server_instance
    try:
        logger.info(f"Starting MCP server on {host}:{port}")
        import uvicorn
        config = uvicorn.Config(
            app=app,  # Use the app we created
            host=host,
            port=port,
            log_level="info",
            access_log=True
        )
        _server_instance = uvicorn.Server(config)
        await _server_instance.serve()
    except Exception as e:
        logger.error(f"Failed to start MCP server: {e}")
        raise

async def stop_mcp_server():
    """Stop the MCP server gracefully"""
    global _server_instance
    if _server_instance:
        logger.info("Stopping MCP server...")
        _server_instance.should_exit = True
        await _server_instance.shutdown()

# Run MCP server
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8082)
