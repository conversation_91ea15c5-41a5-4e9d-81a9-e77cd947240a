"""
MCP Tools for HR operations
"""
import json
import logging
import sys
import os
from typing import Dict, Any, List
from openai import Async<PERSON>penA<PERSON>
from sentence_transformers import SentenceTransformer, util
import dspy
from dspy import InputField, OutputField, Signature


try:
    from app.mcp_module.config import mcp
    from app.settings import settings
except ImportError:
    try:
        from config import mcp
        from app.settings import settings
    except ImportError:
        from fastmcp import FastMCP
        mcp = FastMCP("Recruiter MCP")
        from pydantic_settings import BaseSettings

        class Settings(BaseSettings):
            OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")

        settings = Settings()

    class Settings(BaseSettings):
        OPENAI_API_KEY: str

        class Config:
            env_file = ".env"
            extra = "ignore"

    settings = Settings()

# Configure logging
logger = logging.getLogger(__name__)

# Initialize OpenAI client
openai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)

# Initialize sentence transformer for similarity
sentence_model = SentenceTransformer('all-MiniLM-L6-v2')

# DSPy configuration
dspy.settings.configure(
    lm=dspy.LM(model='gpt-4o-mini', api_key=settings.OPENAI_API_KEY)  # Faster model
)

class ResumeEvaluation(Signature):
    resume = InputField()
    job_description = InputField()
    evaluation = OutputField(
        desc="Return JSON with overall_score (0-100), skills_match (with score, present_skills, missing_skills, explanation), "
             "experience_relevance (with score and explanation), and recommendations (give recommendations for hr if the candidate is right fit or not)"
    )

class ResumeEvaluator(dspy.Module):
    def __init__(self):
        super().__init__()
        self.cot = dspy.ChainOfThought(ResumeEvaluation)

    def forward(self, resume, job_description):
        return self.cot(resume=resume, job_description=job_description).evaluation

# Initialize evaluator
evaluator = ResumeEvaluator()

@mcp.tool()
async def process_hr_query(query: str) -> str:

    try:
        logger.info(f"Processing HR query: {query}")
        response = await openai_client.chat.completions.create(
            model="gpt-4o-mini",  # Much faster than gpt-4-turbo
            messages=[
                {"role": "system", "content": "You are a helpful HR assistant with expertise in recruitment, employee management, and HR best practices."},
                {"role": "user", "content": query}
            ],
            temperature=0.7,
            max_tokens=800  # Reduced for faster response
        )
        result = response.choices[0].message.content
        logger.info("HR query processed successfully")
        return result
    except Exception as e:
        logger.error(f"Error processing HR query: {e}")
        return f"I apologize, but I encountered an error while processing your query: {str(e)}"

@mcp.tool()
async def calculate_resume_similarity(resume_text: str, job_description: str) -> Dict[str, Any]:

    try:
        logger.info("Calculating resume similarity")

        # Calculate embeddings
        resume_embedding = sentence_model.encode(resume_text, convert_to_tensor=True)
        jd_embedding = sentence_model.encode(job_description, convert_to_tensor=True)

        # Calculate similarity
        similarity_score = util.pytorch_cos_sim(resume_embedding, jd_embedding).item()

        # Provide interpretation
        if similarity_score >= 0.8:
            interpretation = "Excellent match"
        elif similarity_score >= 0.6:
            interpretation = "Good match"
        elif similarity_score >= 0.4:
            interpretation = "Moderate match"
        else:
            interpretation = "Poor match"

        result = {
            "similarity_score": similarity_score,
            "interpretation": interpretation,
            "percentage": round(similarity_score * 100, 2)
        }

        logger.info(f"Similarity calculated: {similarity_score}")
        return result

    except Exception as e:
        logger.error(f"Error calculating similarity: {e}")
        return {
            "similarity_score": 0.0,
            "interpretation": "Error in calculation",
            "percentage": 0.0,
            "error": str(e)
        }

@mcp.tool()
async def evaluate_resume_detailed(resume_text: str, job_description: str) -> Dict[str, Any]:

    try:
        logger.info("Performing detailed resume evaluation")

        # Use DSPy evaluator
        response = evaluator(
            resume=resume_text,
            job_description=job_description
        )

        # Parse response if it's a string
        if isinstance(response, str):
            try:
                evaluation_json = json.loads(response)
            except json.JSONDecodeError:
                # If not valid JSON, create a structured response
                evaluation_json = {
                    "overall_score": 75,
                    "skills_match": {
                        "score": 70,
                        "explanation": response
                    },
                    "experience_relevance": {
                        "score": 80,
                        "explanation": "Based on the evaluation response"
                    },
                    "recommendations": response
                }
        else:
            evaluation_json = response

        logger.info("Detailed evaluation completed")
        return evaluation_json

    except Exception as e:
        logger.error(f"Error in detailed evaluation: {e}")
        return {
            "overall_score": 0,
            "skills_match": {
                "score": 0,
                "explanation": f"Error in evaluation: {str(e)}"
            },
            "experience_relevance": {
                "score": 0,
                "explanation": "Could not evaluate due to error"
            },
            "recommendations": "Please try again or contact support",
            "error": str(e)
        }

@mcp.tool()
async def generate_hiring_decision(similarity_score: float, evaluation_json: Dict[str, Any]) -> Dict[str, Any]:

    try:
        logger.info("Generating hiring decision")

        # Extract overall score from evaluation
        overall_score = evaluation_json.get("overall_score", 0)

        # Create decision prompt
        prompt = f"""
        You are a hiring decision assistant. Based on the following information, make a hiring recommendation:

        Semantic Similarity Score: {similarity_score} ({similarity_score * 100:.1f}%)
        Overall Evaluation Score: {overall_score}/100

        Evaluation Details:
        {json.dumps(evaluation_json, indent=2)}

        Please provide:
        1. A clear recommendation (HIRE, CONSIDER, or REJECT)
        2. Key reasons for your decision
        3. Any concerns or highlights
        4. Next steps if applicable

        Be concise but thorough in your analysis.
        """

        response = await openai_client.chat.completions.create(
            model="gpt-4-turbo",
            messages=[
                {"role": "system", "content": "You are an expert hiring decision assistant."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=800
        )

        decision_text = response.choices[0].message.content

        # Determine decision category based on scores
        if similarity_score >= 0.7 and overall_score >= 80:
            decision = "HIRE"
        elif similarity_score >= 0.5 and overall_score >= 60:
            decision = "CONSIDER"
        else:
            decision = "REJECT"

        result = {
            "decision": decision,
            "reasoning": decision_text,
            "similarity_score": similarity_score,
            "overall_score": overall_score,
            "confidence": min(similarity_score + (overall_score / 100), 1.0)
        }

        logger.info(f"Hiring decision generated: {decision}")
        return result

    except Exception as e:
        logger.error(f"Error generating hiring decision: {e}")
        return {
            "decision": "ERROR",
            "reasoning": f"Could not generate decision due to error: {str(e)}",
            "similarity_score": similarity_score,
            "overall_score": evaluation_json.get("overall_score", 0),
            "confidence": 0.0,
            "error": str(e)
        }