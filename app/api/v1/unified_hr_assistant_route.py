from fastapi import APIRouter, UploadFile, File, Form
from typing import Optional
import logfire

from app.orchestrator.graph_recruiter import process_unified_request
from app.services.supabase_client import save_evaluation_to_supabase
from app.services.resume_parser import extract_text_from_pdf

# Initialize router
router = APIRouter()

@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "HR Assistant API",
        "endpoint": "/api/hr-assistant",
        "supported_file_formats": ["PDF", "DOC", "DOCX", "Images (with OCR)"]
    }

@router.post("/hr-assistant")
async def hr_assistant(
    query: str =Form(None),
    resume_file: Optional[UploadFile] = File(None),
    job_description: Optional[str] = Form(None),
    url: Optional[str] = Form(None)
):
    """HR Assistant endpoint with file upload support for resumes"""
    try:
        logfire.info("hr_assistant_request",
                    query=query,
                    has_resume_file=bool(resume_file),
                    has_job_description=bool(job_description),
                    has_url=bool(url),
                    resume_filename=getattr(resume_file, 'filename', None) if resume_file else None)

        resume_text = None

        # Extract text from uploaded resume file using advanced parser
        if resume_file:
            logfire.info("parsing_resume_file",
                        filename=resume_file.filename,
                        content_type=resume_file.content_type)

            parsed_text = extract_text_from_pdf(resume_file)  # Handles PDF, DOC, DOCX, images
            if parsed_text:
                resume_text = parsed_text
                logfire.info("resume_parsed_successfully", text_length=len(resume_text))
            else:
                logfire.warning("resume_parsing_failed", filename=resume_file.filename)
                return {"error": f"Failed to parse resume file: {resume_file.filename}", "status": "error"}

        # Process the request using the unified processing logic
        result = await process_unified_request(
            query=query,
            resume_text=resume_text,
            job_description=job_description,
            url=url
        )

        logfire.info("hr_assistant_completed",
                    status=getattr(result, 'status', 'unknown'),
                    intent=getattr(result, 'intent', 'unknown'))

        # Convert result to dictionary format
        response_data = {
            "status": getattr(result, 'status', 'completed'),
            "intent": getattr(result, 'intent', 'unknown'),
            "response": getattr(result, 'response', ''),
            "similarity_score": getattr(result, 'similarity_score', None),
            "evaluation_json": getattr(result, 'evaluation_json', None),
            "notes": getattr(result, 'notes', None)
        }

        # Save to Supabase if we have evaluation data
        if hasattr(result, 'evaluation_json') and result.evaluation_json:
            try:
                save_evaluation_to_supabase(
                    resume_text=resume_text or "",
                    job_description=job_description or "",
                    similarity_score=str(getattr(result, 'similarity_score', 0)),
                    evaluation_json=result.evaluation_json
                )
                logfire.info("evaluation_saved_to_supabase")
            except Exception as e:
                logfire.warning("supabase_save_failed", error=str(e))

        return response_data

    except Exception as e:
        logfire.error("hr_assistant_error", error=str(e), error_type=type(e).__name__)
        import traceback
        logfire.error("hr_assistant_traceback", traceback=traceback.format_exc())
        return {"error": f"Request processing failed: {str(e)}", "status": "error"}
