from fastapi import APIRouter, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse
from pydantic import BaseModel
from typing import Optional
import logfire
import io
import json

from app.orchestrator.graph_recruiter import process_unified_request
from app.services.supabase_client import save_evaluation_to_supabase, save_chat_history

# Initialize router
router = APIRouter()

def extract_text_from_file(file: UploadFile) -> str:
    """
    Simple text extraction from uploaded files
    """
    try:
        content = file.file.read()

        if file.content_type == "text/plain":
            return content.decode('utf-8')
        elif file.content_type == "application/pdf":
            # Simple PDF text extraction using PyPDF
            try:
                import PyPDF2
                pdf_reader = PyPDF2.PdfReader(io.BytesIO(content))
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text.strip()
            except ImportError:
                return "PDF processing not available. Please install PyPDF2 or provide text format."
        else:
            # Try to decode as text for other file types
            try:
                return content.decode('utf-8')
            except UnicodeDecodeError:
                return f"Unsupported file type: {file.content_type}. Please provide a text or PDF file."
    except Exception as e:
        return f"Error reading file: {str(e)}"
    finally:
        file.file.seek(0)  # Reset file pointer

class UnifiedHRRequest(BaseModel):
    query: Optional[str] = None
    job_description: Optional[str] = None
    resume_text: Optional[str] = None
    url: Optional[str] = None

@router.post("/hr-assistant-unified")
async def unified_hr_assistant(request: UnifiedHRRequest):
    try:
        # Extract fields from request
        query = request.query
        job_description = request.job_description
        resume_text = request.resume_text
        url = request.url

        logfire.info("🚀 Starting unified HR assistant request",
                    query=query,
                    has_resume_text=bool(resume_text),
                    has_jd=bool(job_description))
        # Determine intent based on provided inputs
        intent = None
        reasoning = ""

        logfire.info("🔍 Intent classification inputs",
                    job_description_present=bool(job_description),
                    resume_text_present=bool(resume_text))

        if job_description and resume_text:
            intent = "resume_vs_jd"
            reasoning = "Intent determined from job description and resume inputs."
            logfire.info("✅ Intent set to resume_vs_jd")
        elif url and job_description:
            intent = "portal_candidate_eval"
            reasoning = "Intent determined from URL and job description inputs."
        elif query:
            # Let the unified agent classify the intent
            reasoning = f"Intent will be classified by unified agent for query: '{query}'"

        # Heuristic override for sourcing queries
        if query and "boolean search" in query.lower():
            logfire.info("Heuristic override triggered", reason="Detected sourcing query")
            intent = "general_hr_query"

        logfire.info("Processing request", query=query, intent=intent, reasoning=reasoning)

        # Validate inputs based on intent
        if intent == "resume_vs_jd" and (not resume_text or not job_description):
            # Override intent if we don't have the required inputs
            if query:
                intent = None  # Let the agent classify it
                reasoning = f"Intent overridden to general query due to missing resume/JD for query: '{query}'"
            else:
                return {"error": "Resume vs JD evaluation requires both resume file and job description"}

        # Fast path for general HR queries to avoid heavy graph recruiter
        if query and not resume_text and not job_description and not url:
            logfire.info("Fast path for general HR query", query=query)
            try:
                # Use Agent Gateway SSE for general HR queries
                from app.mcp_module.client import classify_intent, process_hr_query

                # Classify intent quickly
                classified_intent = await classify_intent(query)
                logfire.info("Intent classified", intent=classified_intent)

                if classified_intent == "general_hr_query":
                    # Process directly without graph recruiter
                    logfire.info("Processing HR query directly")
                    response = await process_hr_query(query)

                    return {
                        "intent": classified_intent,
                        "response": response,
                        "status": "completed",
                        "reasoning": "Processed directly via HTTP MCP (fast path)"
                    }
            except Exception as e:
                logfire.error("Fast path failed", error=str(e))
                # Fall back to full graph recruiter if fast path fails

        # Process request through simple unified agent (no infinite loops)
        from simple_graph_recruiter import process_unified_request_simple
        result = await process_unified_request_simple(
            query=query,
            resume_text=resume_text,
            job_description=job_description,
            url=url,
            intent=intent
        )

        # Handle errors
        if result.error:
            logfire.error("Unified agent error", error=result.error, intent=result.intent)
            return {"error": result.error, "intent": result.intent}

        # Format response based on the determined intent
        if result.intent == "general_hr_query":
            if result.response:
                clean_response = result.response.replace('\n', ' ')
                save_chat_history(query, clean_response)
                return {
                    "intent": result.intent,
                    "response": clean_response,
                    "reasoning": reasoning
                }
            else:
                return {"error": "No response generated for HR query", "intent": result.intent}

        elif result.intent == "portal_candidate_eval":
            return {
                "intent": result.intent,
                "result": result.portal_results,
                "reasoning": reasoning
            }

        elif result.intent == "resume_vs_jd":
            # Save evaluation results to database
            if result.similarity_score is not None and result.evaluation_json:
                save_result = save_evaluation_to_supabase(
                    resume_text,
                    job_description,
                    result.similarity_score,
                    result.evaluation_json
                )
            else:
                save_result = {"status": "not_saved", "reason": "incomplete_evaluation"}

            return {
                "intent": result.intent,
                "similarity_score": result.similarity_score,
                "evaluation_json": result.evaluation_json,
                "status": result.status,
                "notes": result.notes,
                "save_status": save_result,
                "reasoning": reasoning
            }

        else:
            return {"error": "Unable to determine intent", "intent": result.intent}

    except Exception as e:
        logfire.error("FastAPI server error", error=str(e), error_type=type(e).__name__)
        import traceback
        logfire.error("Full traceback", traceback=traceback.format_exc())
        return {"error": "Internal server error", "details": str(e), "error_type": type(e).__name__}
