from fastapi import APIRouter, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse
from pydantic import BaseModel
from typing import Optional
import logfire
import io
import json

from app.orchestrator.graph_recruiter import process_unified_request
from app.services.supabase_client import save_evaluation_to_supabase, save_chat_history

# Initialize router
router = APIRouter()

@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "HR Assistant API",
        "endpoints": {
            "json": "/api/hr-assistant-unified",
            "upload": "/api/hr-assistant-unified-upload"
        }
    }

def extract_text_from_file(file: UploadFile) -> str:
    """
    Simple text extraction from uploaded files with robust error handling
    """
    if not file or not file.file:
        return ""

    try:
        content = file.file.read()

        # Handle empty files
        if not content:
            return ""

        # Determine file type from content and filename
        filename = getattr(file, 'filename', '') or ""
        content_type = getattr(file, 'content_type', '') or ""

        # PDF detection (by content type, extension, or magic bytes)
        if (content_type == "application/pdf" or
            filename.lower().endswith('.pdf') or
            content.startswith(b'%PDF')):
            try:
                import PyPDF2
                pdf_reader = PyPDF2.PdfReader(io.BytesIO(content))
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text.strip()
            except ImportError:
                return "PDF processing not available. Please install PyPDF2 or provide text format."
            except Exception as e:
                return f"Error processing PDF: {str(e)}"

        # Text file handling with multiple encoding attempts
        else:
            # Try UTF-8 first
            try:
                return content.decode('utf-8')
            except UnicodeDecodeError:
                pass

            # Try other common encodings
            for encoding in ['latin-1', 'cp1252', 'iso-8859-1', 'utf-16']:
                try:
                    return content.decode(encoding)
                except (UnicodeDecodeError, UnicodeError):
                    continue

            # If all encodings fail, return a safe error message
            safe_filename = filename if isinstance(filename, str) else 'unknown'
            return f"Error: Unable to decode file '{safe_filename}'. Please ensure it's a valid text or PDF file."

    except Exception as e:
        # Ensure error message doesn't contain problematic bytes
        safe_filename = getattr(file, 'filename', 'unknown') or 'unknown'
        if isinstance(safe_filename, bytes):
            safe_filename = 'unknown'
        return f"Error reading file '{safe_filename}': {str(e)}"
    finally:
        try:
            file.file.seek(0)  # Reset file pointer
        except:
            pass  # Ignore seek errors

class UnifiedHRRequest(BaseModel):
    query: Optional[str] = None
    job_description: Optional[str] = None
    resume_text: Optional[str] = None
    url: Optional[str] = None

@router.post("/hr-assistant-unified")
async def unified_hr_assistant(request: UnifiedHRRequest):
    """JSON-based HR assistant endpoint"""
    try:
        # Extract fields from request
        query = request.query
        job_description = request.job_description
        resume_text = request.resume_text
        url = request.url

        logfire.info("🚀 Starting unified HR assistant request",
                    query=query,
                    has_resume_text=bool(resume_text),
                    has_jd=bool(job_description))
        # Determine intent based on provided inputs
        intent = None
        reasoning = ""

        logfire.info("🔍 Intent classification inputs",
                    job_description_present=bool(job_description),
                    resume_text_present=bool(resume_text))

        if job_description and resume_text:
            intent = "resume_vs_jd"
            reasoning = "Intent determined from job description and resume inputs."
            logfire.info("✅ Intent set to resume_vs_jd")
        elif url and job_description:
            intent = "portal_candidate_eval"
            reasoning = "Intent determined from URL and job description inputs."
        elif query:
            # Let the unified agent classify the intent
            reasoning = f"Intent will be classified by unified agent for query: '{query}'"

        # Heuristic override for sourcing queries
        if query and "boolean search" in query.lower():
            logfire.info("Heuristic override triggered", reason="Detected sourcing query")
            intent = "general_hr_query"

        logfire.info("Processing request", query=query, intent=intent, reasoning=reasoning)

        # Validate inputs based on intent
        if intent == "resume_vs_jd" and (not resume_text or not job_description):
            # Override intent if we don't have the required inputs
            if query:
                intent = None  # Let the agent classify it
                reasoning = f"Intent overridden to general query due to missing resume/JD for query: '{query}'"
            else:
                return {"error": "Resume vs JD evaluation requires both resume file and job description"}

        # Fast path for general HR queries to avoid heavy graph recruiter
        if query and not resume_text and not job_description and not url:
            logfire.info("Fast path for general HR query", query=query)
            try:
                # Use Agent Gateway SSE for general HR queries
                from app.mcp_module.client import classify_intent, process_hr_query

                # Classify intent quickly
                classified_intent = await classify_intent(query)
                logfire.info("Intent classified", intent=classified_intent)

                if classified_intent == "general_hr_query":
                    # Process directly without graph recruiter
                    logfire.info("Processing HR query directly")
                    response = await process_hr_query(query)

                    return {
                        "intent": classified_intent,
                        "response": response,
                        "status": "completed",
                        "reasoning": "Processed directly via HTTP MCP (fast path)"
                    }
            except Exception as e:
                logfire.error("Fast path failed", error=str(e))
                # Fall back to full graph recruiter if fast path fails

        # Process request through unified agent
        from app.orchestrator.graph_recruiter import process_unified_request
        result = await process_unified_request(
            query=query,
            resume_text=resume_text,
            job_description=job_description,
            url=url,
            intent=intent
        )

        # Handle errors
        if result.error:
            logfire.error("Unified agent error", error=result.error, intent=result.intent)
            return {"error": result.error, "intent": result.intent}

        # Format response based on the determined intent
        if result.intent == "general_hr_query":
            if result.response:
                clean_response = result.response.replace('\n', ' ')
                save_chat_history(query, clean_response)
                return {
                    "intent": result.intent,
                    "response": clean_response,
                    "reasoning": reasoning
                }
            else:
                return {"error": "No response generated for HR query", "intent": result.intent}

        elif result.intent == "portal_candidate_eval":
            return {
                "intent": result.intent,
                "result": result.portal_results,
                "reasoning": reasoning
            }

        elif result.intent == "resume_vs_jd":
            # Save evaluation results to database
            if result.similarity_score is not None and result.evaluation_json:
                save_result = save_evaluation_to_supabase(
                    resume_text,
                    job_description,
                    result.similarity_score,
                    result.evaluation_json
                )
            else:
                save_result = {"status": "not_saved", "reason": "incomplete_evaluation"}

            return {
                "intent": result.intent,
                "similarity_score": result.similarity_score,
                "evaluation_json": result.evaluation_json,
                "status": result.status,
                "notes": result.notes,
                "save_status": save_result,
                "reasoning": reasoning
            }

        else:
            return {"error": "Unable to determine intent", "intent": result.intent}

    except Exception as e:
        logfire.error("FastAPI server error", error=str(e), error_type=type(e).__name__)
        import traceback
        logfire.error("Full traceback", traceback=traceback.format_exc())
        return {"error": "Internal server error", "details": str(e), "error_type": type(e).__name__}


@router.post("/hr-assistant-unified-upload")
async def unified_hr_assistant_upload(
    query: str = Form(...),
    resume_file: Optional[UploadFile] = File(None),
    job_description_file: Optional[UploadFile] = File(None),
    resume_text: Optional[str] = Form(None),
    job_description: Optional[str] = Form(None),
    url: Optional[str] = Form(None)
):
    """File upload-based HR assistant endpoint"""
    try:
        # Extract text from uploaded files
        if resume_file:
            resume_text = extract_text_from_file(resume_file)

        if job_description_file:
            job_description = extract_text_from_file(job_description_file)

        # Create request object for processing
        request_data = UnifiedHRRequest(
            query=query,
            job_description=job_description,
            resume_text=resume_text,
            url=url
        )

        # Process using the same logic as the JSON endpoint
        return await unified_hr_assistant(request_data)

    except Exception as e:
        logfire.error("upload_endpoint_error", error=str(e))
        return {"error": str(e), "status": "error"}
