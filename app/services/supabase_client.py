from supabase import create_client, Client
from app.settings import settings

supabase: Client = create_client(settings.SUPABASE_URL, settings.SUPABASE_API_KEY)

def save_evaluation_to_supabase(resume_text: str, job_description: str, similarity_score: str, evaluation_json: dict):
    """Save the evaluation result into Supabase"""
    data = {
        "resume_text": resume_text,
        "job_description": job_description,
        "similarity_score": similarity_score,
        "evaluation_result": evaluation_json
    }
    response = supabase.table("evaluation_results").insert(data).execute()
    return response

def save_chat_history(query: str, response: str):
    data = {
        "query": query,
        "response": response,
    }
    return supabase.table("chat_history").insert(data).execute()