# import re
# import logging
# from PyPDF2 import PdfReader
# from fastapi import UploadFile
#
# _logger = logging.getLogger(__name__)
#
#
# def extract_text_from_pdf(resume_file: UploadFile):
#     try:
#         text = ""
#         # file pointer to the beginning
#         if resume_file:
#             resume_file.file.seek(0)
#             reader = PdfReader(resume_file.file)
#             if reader:
#                 for page in reader.pages:
#                     page_text = page.extract_text()
#                     if page_text:
#                         text += page_text + "\n"
#                 text = re.sub(r'\n+', ' ', text)  # Replace multiple \n with space
#                 text = re.sub(r'\s{2,}', ' ', text)  # Replace multiple spaces with single space
#                 _logger.info( "Resume extraction process completed..." )
#                 return text.strip() if text else None
#             return None
#         return None
#
#     except Exception as e:
#         _logger.error( "Resume extraction process Failed" )
#         print(f"PDF extraction failed: {e}")
#         return " "

import re
import logging
import filetype
from fastapi import UploadFile
from PyPDF2 import PdfReader
from docx import Document
from PIL import Image
import pytesseract

_logger = logging.getLogger(__name__)

def clean_text(text: str):
    text = re.sub(r'\n+', ' ', text)
    text = re.sub(r'\s{2,}', ' ', text)
    return text.strip()

def extract_text_from_pdf(upload_file: UploadFile):
    try:
        file_bytes = upload_file.file.read()
        kind = filetype.guess(file_bytes)
        content_type = kind.mime if kind else upload_file.content_type

        text = ""

        if content_type == "application/pdf":
            upload_file.file.seek(0)
            reader = PdfReader(upload_file.file)
            for page in reader.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"

        elif content_type in ["application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/msword"]:
            upload_file.file.seek(0)
            doc = Document(upload_file.file)
            for para in doc.paragraphs:
                text += para.text + "\n"

        elif content_type.startswith("image/"):
            img = Image.open(upload_file.file)
            text = pytesseract.image_to_string(img)

        else:
            _logger.warning("Unsupported file format: %s", content_type)
            return None

        _logger.info("File text extraction completed.")
        return clean_text(text) if text else None

    except Exception as e:
        _logger.error("File text extraction failed.")
        print(f"Extraction error: {e}")
        return None
