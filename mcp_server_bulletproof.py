#!/usr/bin/env python3
"""
Bulletproof MCP Server - Guaranteed to work with Agent Gateway
Fixed JSON format and stdio communication
"""
import json
import sys
import os
import time
import asyncio
import httpx

# Ensure unbuffered output for Agent Gateway
os.environ['PYTHONUNBUFFERED'] = '1'
sys.stdout.reconfigure(line_buffering=True)
sys.stderr.reconfigure(line_buffering=True)


def log(message):
    """Log to stderr only"""
    print(f"[MCP] {message}", file=sys.stderr, flush=True)

async def call_fastapi_tool(tool_name: str, arguments: dict) -> str:
    """Call your actual FastAPI tools"""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            if tool_name == "process_hr_query":
                log(f"Calling FastAPI tool: {tool_name} with query: {arguments.get('query', '')}")
                response = await client.post(
                    "http://localhost:8082/tools/call",
                    json={"name": tool_name, "arguments": arguments},
                    timeout=10.0
                )
                if response.status_code == 200:
                    result = response.json()
                    return result.get("content", str(result))
                else:
                    return f"Error calling FastAPI tool: {response.status_code}"

            elif tool_name == "calculate_resume_similarity":
                log(f"Calling FastAPI tool: {tool_name} with args: {arguments}")
                response = await client.post(
                    "http://localhost:8082/tools/call",
                    json={"name": tool_name, "arguments": arguments}
                )
                log(f"FastAPI response status: {response.status_code}")
                if response.status_code == 200:
                    result = response.json()
                    log(f"FastAPI response: {result}")
                    return result.get("content", str(result))
                else:
                    log(f"FastAPI error response: {response.text}")
                    return f"Error calling FastAPI tool: {response.status_code} - {response.text}"

            elif tool_name == "evaluate_resume_detailed":
                response = await client.post(
                    "http://localhost:8081/tools/call",
                    json={"name": tool_name, "arguments": arguments}
                )
                if response.status_code == 200:
                    return response.text
                else:
                    return f"Error calling FastAPI tool: {response.status_code}"

            elif tool_name == "generate_hiring_decision":
                response = await client.post(
                    "http://localhost:8081/tools/call",
                    json={"name": tool_name, "arguments": arguments}
                )
                if response.status_code == 200:
                    return response.text
                else:
                    return f"Error calling FastAPI tool: {response.status_code}"

            else:
                return f"Unknown tool: {tool_name}"

    except Exception as e:
        log(f"Error calling FastAPI tool {tool_name}: {e}")
        # Fallback to simple responses if FastAPI is not available
        if tool_name == "process_hr_query":
            query = arguments.get("query", "")
            return f"**Job Description Response for: {query}**\n\nThis is a fallback response. Please ensure your FastAPI MCP server is running on port 8081."
        else:
            return f"Error calling tool {tool_name}: {str(e)}"


def send_json(data):
    """Send JSON to stdout with exact Agent Gateway format"""
    try:
        # Use compact JSON with no spaces - exactly what Agent Gateway expects
        json_str = json.dumps(data, separators=(',', ':'), ensure_ascii=True)
        print(json_str, flush=True)

        # Small delay to ensure Agent Gateway processes the response completely
        time.sleep(0.1)

        log(f"SENT: {json_str}")
    except Exception as e:
        log(f"ERROR sending JSON: {e}")
        # Try to continue anyway
        pass


def main():
    """Main server loop"""
    log("Bulletproof MCP Server starting...")

    # Track initialization state
    initialized = False
    request_count = 0

    try:
        while True:
            # Read line from stdin
            line = sys.stdin.readline()

            if not line:
                log("EOF - shutting down")
                break

            line = line.strip()
            if not line:
                continue

            log(f"RECV: {line}")

            try:
                request = json.loads(line)
                request_count += 1
                log(f"Processing request #{request_count}")
            except json.JSONDecodeError as e:
                log(f"JSON decode error: {e}")
                continue

            # Extract request details
            method = request.get("method", "")
            request_id = request.get("id")
            params = request.get("params", {})

            # Handle notifications (no response needed)
            if request_id is None:
                if method == "notifications/initialized":
                    log("Initialized notification received")
                continue

            # Generate response based on method
            response = None

            if method == "initialize":
                if initialized:
                    log("Responding to duplicate initialize request")
                else:
                    log("Handling first initialize request")
                    initialized = True

                # Always send response to keep communication channel open
                response = {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {
                            "tools": {},
                            "prompts": {}
                        },
                        "serverInfo": {
                            "name": "bulletproof-mcp-server",
                            "version": "1.0.0"
                        }
                    }
                }

            elif method == "tools/list":
                log("Handling tools/list request")
                response = {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "tools": [
                            {
                                "name": "process_hr_query",
                                "description": "Process HR queries",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "query": {"type": "string"}
                                    },
                                    "required": ["query"]
                                }
                            },
                            {
                                "name": "calculate_resume_similarity",
                                "description": "Calculate resume similarity",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "resume_text": {"type": "string"},
                                        "job_description": {"type": "string"}
                                    },
                                    "required": ["resume_text", "job_description"]
                                }
                            },
                            {
                                "name": "evaluate_resume_detailed",
                                "description": "Evaluate resume in detail",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "resume_text": {"type": "string"},
                                        "job_description": {"type": "string"}
                                    },
                                    "required": ["resume_text", "job_description"]
                                }
                            },
                            {
                                "name": "generate_hiring_decision",
                                "description": "Generate hiring decision",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "similarity_score": {"type": "number"},
                                        "evaluation_json": {"type": "object"}
                                    },
                                    "required": ["similarity_score", "evaluation_json"]
                                }
                            }
                        ]
                    }
                }

            elif method == "tools/call":
                log(f"Handling tools/call request for: {params.get('name', 'unknown')}")
                tool_name = params.get("name", "")
                arguments = params.get("arguments", {})

                # Call actual FastAPI tools
                try:
                    log(f"About to call FastAPI tool: {tool_name}")
                    result_text = asyncio.run(call_fastapi_tool(tool_name, arguments))
                    log(f"FastAPI tool result: {str(result_text)[:100]}...")
                except Exception as e:
                    log(f"Error calling FastAPI tool: {e}")
                    # Provide a working fallback response
                    if tool_name == "process_hr_query":
                        query = arguments.get("query", "")
                        result_text = f"**Python Developer Job Description:**\n\n1. Develop and maintain Python applications using frameworks like Django/Flask\n2. Work with databases, APIs, and data processing libraries\n3. Collaborate with teams and deploy applications to cloud platforms\n\n(Note: This is a fallback response - FastAPI server may not be accessible)"
                    else:
                        result_text = f"Tool {tool_name} executed with fallback response due to: {str(e)}"

                response = {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "content": [
                            {
                                "type": "text",
                                "text": result_text
                            }
                        ]
                    }
                }

            elif method == "prompts/list":
                log("Handling prompts/list request")
                response = {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "prompts": [
                            {
                                "name": "classify_hr_query_intent",
                                "description": "Classify HR query intent",
                                "arguments": [
                                    {
                                        "name": "query",
                                        "description": "Query to classify",
                                        "required": True
                                    }
                                ]
                            }
                        ]
                    }
                }

            elif method == "prompts/get":
                log("Handling prompts/get request")
                prompt_name = params.get("name", "")
                arguments = params.get("arguments", {})

                if prompt_name == "classify_hr_query_intent":
                    query = arguments.get("query", "").lower()

                    # Simple classification
                    if any(word in query for word in ["resume", "cv", "job description", "similarity"]):
                        intent = "resume_vs_jd"
                    elif any(word in query for word in ["candidate", "evaluate", "assessment"]):
                        intent = "portal_candidate_eval"
                    else:
                        intent = "general_hr_query"

                    response = {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "messages": [
                                {
                                    "role": "user",
                                    "content": {
                                        "type": "text",
                                        "text": intent
                                    }
                                }
                            ]
                        }
                    }
                else:
                    response = {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "error": {
                            "code": -32601,
                            "message": f"Unknown prompt: {prompt_name}"
                        }
                    }

            else:
                log(f"Unknown method: {method}")
                response = {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32601,
                        "message": f"Method not found: {method}"
                    }
                }

            # Send response
            if response:
                send_json(response)

    except KeyboardInterrupt:
        log("Keyboard interrupt received")
    except Exception as e:
        log(f"Fatal error: {e}")
        import traceback
        traceback.print_exc(file=sys.stderr)

    log("Server shutting down")


if __name__ == "__main__":
    main()
