# HTTP MCP Client with SSE Support

## Overview

This document describes the implementation of HTTP-based MCP (Model Context Protocol) client with Server-Sent Events (SSE) support as recommended by your TL. This replaces the previous in-process MCP client with a more scalable, streamable HTTP transport layer.

## Architecture

### Previous Implementation (In-Process)
```
Application → InProcessMCPClient → Direct Function Calls → MCP Tools/Prompts
```

### New Implementation (HTTP with SSE)
```
Application → HTTPMCPClient → HTTP/SSE → MCP Server → MCP Tools/Prompts
```

## Key Components

### 1. HTTPMCPClient (`app/mcp_module/client.py`)

The new HTTP-based client that supports:
- **HTTP Transport**: Uses `httpx.AsyncClient` for HTTP communication
- **SSE Streaming**: Supports Server-Sent Events for real-time streaming responses
- **Automatic Fallbacks**: Falls back to in-process client and OpenAI when HTTP fails
- **Connection Management**: Proper async context manager support

#### Key Methods:
- `call_prompt()`: Call MCP prompts via HTTP
- `call_tool()`: Call MCP tools via HTTP
- `call_tool_streaming()`: Call MCP tools with SSE streaming

### 2. HTTP Endpoints (`app/mcp_module/config.py`)

Added HTTP endpoints to the FastMCP server:
- `GET /health`: Health check endpoint
- `POST /prompts/call`: Call MCP prompts
- `POST /tools/call`: Call MCP tools
- `POST /tools/call/stream`: Call MCP tools with SSE streaming

### 3. Integration (`app/orchestrator/graph_recruiter.py`)

Updated the graph recruiter to use HTTP MCP with fallbacks:
- Intent classification uses `classify_intent_http()`
- HR query processing uses `process_hr_query_http()`
- Similarity calculation uses HTTP MCP client
- All operations have fallback to in-process MCP

## Benefits

### 1. Streamable Responses
- Real-time streaming of responses using SSE
- Better user experience with progressive content delivery
- Reduced perceived latency

### 2. Better Scalability
- HTTP transport allows for distributed deployment
- MCP server can be scaled independently
- Multiple clients can connect to the same MCP server

### 3. Improved Reliability
- Multiple fallback layers:
  1. HTTP MCP → In-Process MCP → OpenAI Direct
- Graceful degradation when services are unavailable
- Connection pooling and timeout management

### 4. Transport Flexibility
- Can easily switch between HTTP and other transports
- Supports both synchronous and streaming responses
- Compatible with existing MCP protocol

## Usage Examples

### Basic Usage
```python
from app.mcp_module.client import classify_intent_http, process_hr_query_http

# Intent classification
intent = await classify_intent_http("What are good hiring practices?")

# HR query processing
response = await process_hr_query_http("Explain the STAR method")
```

### Streaming Usage
```python
from app.mcp_module.client import process_hr_query_streaming

async for chunk in process_hr_query_streaming("Explain technical interviews"):
    print(chunk, end='', flush=True)
```

### Direct Client Usage
```python
from app.mcp_module.client import HTTPMCPClient

async with HTTPMCPClient() as client:
    # Regular tool call
    result = await client.call_tool("process_hr_query", {"query": "..."})
    
    # Streaming tool call
    async for chunk in client.call_tool_streaming("process_hr_query", {"query": "..."}):
        print(chunk, end='')
```

## Configuration

### MCP Server
The MCP server runs on `http://localhost:8080` by default. Start it with:
```bash
python run_mcp_server.py
```

### Client Configuration
```python
# Default configuration (localhost:8080)
client = HTTPMCPClient()

# Custom configuration
client = HTTPMCPClient("http://your-mcp-server:8080")
```

## Testing

### Run Tests
```bash
# Test the HTTP MCP implementation
python test_http_mcp.py

# Run usage examples
python example_http_mcp_usage.py
```

### Test Coverage
- Connection testing
- Intent classification
- HR query processing
- Streaming responses
- Tool calls
- Error handling and fallbacks

## Migration Guide

### For Existing Code
The new implementation maintains backward compatibility:

```python
# Old way (still works)
from app.mcp_module.client import classify_intent_direct, process_hr_query_direct

# New way (recommended)
from app.mcp_module.client import classify_intent_http, process_hr_query_http
```

### For New Code
Use the HTTP variants for new implementations:
- `classify_intent_http()` instead of `classify_intent_direct()`
- `process_hr_query_http()` instead of `process_hr_query_direct()`
- `HTTPMCPClient()` instead of `InProcessMCPClient()`

## Monitoring and Debugging

### Logging
The implementation includes comprehensive logging:
```python
import logging
logging.getLogger('app.mcp_module.client').setLevel(logging.DEBUG)
```

### Health Checks
Monitor MCP server health:
```bash
curl http://localhost:8080/health
```

### Fallback Behavior
The system automatically falls back in this order:
1. HTTP MCP Client
2. In-Process MCP Client  
3. Direct OpenAI API calls

## Performance Considerations

### Connection Pooling
- Uses `httpx.AsyncClient` with connection pooling
- Configurable timeouts (default: 30 seconds)
- Automatic connection management

### Streaming Benefits
- Reduced memory usage for large responses
- Better perceived performance
- Real-time user feedback

### Caching
Consider implementing response caching for:
- Intent classification results
- Frequently asked HR queries
- Static content responses

## Security

### Transport Security
- Use HTTPS in production
- Implement proper authentication if needed
- Consider rate limiting

### Error Handling
- Sensitive information is not exposed in error messages
- Graceful degradation prevents service disruption
- Comprehensive logging for debugging

## Future Enhancements

### Planned Features
1. **Authentication**: Add API key or JWT authentication
2. **Load Balancing**: Support multiple MCP server instances
3. **Caching**: Implement intelligent response caching
4. **Metrics**: Add performance and usage metrics
5. **WebSocket Support**: Alternative to SSE for bidirectional communication

### Configuration Options
1. **Retry Logic**: Configurable retry attempts and backoff
2. **Circuit Breaker**: Prevent cascading failures
3. **Request/Response Middleware**: Custom processing hooks
