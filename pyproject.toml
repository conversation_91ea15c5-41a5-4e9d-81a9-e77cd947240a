[project]
name = "recruiter-assistant"
version = "0.1.0"
description = ""
authors = [
    { name = "Your Name", email = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.10,<3.13"

#dependencies = [
#    "aiohappyeyeballs==2.6.1",
#    "aiohttp==3.11.18",
#    "aiosignal==1.3.2",
#    "annotated-types==0.7.0",
#    "anyio==4.9.0",
#    "attrs==25.3.0",
#    "certifi==2025.4.26",
#    "charset-normalizer==3.4.1",
#    "click==8.1.8",
#    "colorama==0.4.6",
#    "deprecation==2.1.0",
#    "fastapi==0.115.12",
#    "filelock==3.18.0",
#    "frozenlist==1.6.0",
#    "fsspec[http]>=2023.1.0,<=2023.10.0",
#    "gotrue==2.12.0",
#    "h11==0.16.0",
#    "h2==4.2.0",
#    "hpack==4.1.0",
#    "httpcore==1.0.9",
#    "httpx==0.28.1",
#    "huggingface-hub==0.30.2",
#    "hyperframe==6.1.0",
#    "idna==3.10",
#    "iniconfig==2.1.0",
#    "Jinja2==3.1.6",
#    "joblib==1.4.2",
#    "MarkupSafe==3.0.2",
#    "mpmath==1.3.0",
#    "multidict==6.4.3",
#    "networkx==3.4.2",
#    "numpy==2.2.5",
#    "packaging>=23.2,<25",
#    "pillow==11.2.1",
#    "pluggy==1.5.0",
#    "postgrest==1.0.1",
#    "propcache==0.3.1",
#    "pydantic==2.11.3",
#    "pydantic-core==2.33.1",
#    "PyJWT==2.10.1",
#    "pypdf==5.4.0",
#    "pytest==8.3.5",
#    "pytest-mock==3.14.0",
#    "python-dateutil==2.9.0.post0",
#    "python-multipart==0.0.20",
#    "PyYAML==6.0.2",
#    "realtime==2.4.2",
#    "regex==2024.11.6",
#    "requests==2.32.3",
#    "safetensors==0.5.3",
#    "scikit-learn==1.6.1",
#    "scipy==1.15.2",
#    "sentence-transformers==4.1.0",
#    "setuptools==79.0.1",
#    "six==1.17.0",
#    "sniffio==1.3.1",
#    "starlette==0.46.2",
#    "storage3==0.11.3",
#    "StrEnum==0.4.15",
#    "supabase==2.15.0",
#    "supafunc==0.9.4",
#    "sympy==1.13.3",
#    "threadpoolctl==3.6.0",
#    "tokenizers==0.21.1",
#    "torch==2.7.0",
#    "tqdm==4.67.1",
#    "transformers==4.51.3",
#    "typing-inspection==0.4.0",
#    "typing_extensions==4.13.2",
#    "urllib3==2.4.0",
#    "uvicorn==0.34.2",
#    "websockets==14.2",
#    "yarl==1.20.0",
#    "pypdf2~=3.0.1",
#    "google~=3.0.0",
#    "dotenv~=0.9.9",
#    "python-dotenv~=1.1.0",
#    "langgraph==0.3.34",
#    "langgraph-prebuilt==0.1.8",
#    "langchain-core==0.2.43",
#    "apscheduler>=3.11.0,<4.0.0",
#    "logfire>=3.16.1,<4.0.0",
#    "pydantic-settings>=2.9.1,<3.0.0",
#    "cloudpickle>=3.1.1,<4.0.0",
#    "dspy>=2.6.24,<3.0.0",
#]
dependencies = [
    "aiohappyeyeballs==2.6.1",
    "aiohttp==3.11.18",
    "aiosignal==1.3.2",
    "alembic==1.16.1",
    "annotated-types==0.7.0",
    "anyio==4.9.0",
    "APScheduler==3.11.0",
    "asgiref==3.8.1",
    "asyncer==0.0.8",
    "asyncio==3.4.3",
    "attrs==25.3.0",
    "backoff==2.2.1",
    "beautifulsoup4==4.13.4",
    "browserbase==1.4.0",
    "build==1.2.2.post1",
    "CacheControl==0.14.3",
    "cachetools==5.5.2",
    "certifi==2025.4.26",
    "cffi==1.17.1",
    "charset-normalizer==3.4.1",
    "cleo==2.1.0",
    "click==8.1.8",
    "click-default-group==1.2.4",
    "cloudpickle==3.1.1",
    "colorama==0.4.6",
    "colorlog==6.9.0",
    "condense-json==0.1.3",
    "crashtest==0.4.1",
    "dataclasses-json==0.6.7",
    "datasets==3.6.0",
    "Deprecated==1.2.18",
    "deprecation==2.1.0",
    "dill==0.3.8",
    "diskcache==5.6.3",
    "distlib==0.3.9",
    "distro==1.9.0",
    "docstring_parser==0.16",
    "dotenv==0.9.9",
    "dspy==2.6.24",
    "dspy-ai==2.6.24",
    "dulwich==0.22.8",
    "eval_type_backport==0.2.2",
    "executing==2.2.0",
    "fastapi==0.115.12",
    "fastjsonschema==2.21.1",
    "filelock==3.18.0",
    "filetype==1.2.0",
    "findpython==0.6.3",
    "frozenlist==1.6.0",
    "fsspec==2023.10.0",
    "google==3.0.0",
    "google-ai-generativelanguage==0.6.18",
    "google-api-core==2.24.2",
    "google-auth==2.40.1",
    "googleapis-common-protos==1.70.0",
    "gotrue==2.12.0",
    "graphviz==0.20.3",
    "greenlet==3.2.2",
    "grpcio==1.72.0rc1",
    "grpcio-status (>=1.71,<2.0)",
    "h11==0.16.0",
    "h2==4.2.0",
    "hpack==4.1.0",
    "httpcore==1.0.9",
    "httpx==0.28.1",
    "httpx-aiohttp==0.1.4",
    "httpx-sse==0.4.0",
    "huggingface-hub==0.30.2",
    "hyperframe==6.1.0",
    "idna==3.10",
    "importlib_metadata==8.6.1",
    "iniconfig==2.1.0",
    "installer==0.7.0",
    "instructor==1.7.2",
    "jaraco.classes==3.4.0",
    "jaraco.context==6.0.1",
    "jaraco.functools==4.1.0",
    "Jinja2==3.1.6",
    "jiter==0.8.0",
    "joblib==1.4.2",
    "json_repair==0.46.0",
    "jsonpatch==1.33",
    "jsonpointer==3.0.0",
    "jsonschema==4.24.0",
    "jsonschema-specifications==2025.4.1",
    "keyring==25.6.0",
    "krippendorff==0.8.1",
    "langchain==0.3.25",
    "langchain-community==0.3.24",
    "langchain-core==0.3.59",
    "langchain-google-genai==2.1.4",
    "langchain-openai==0.3.17",
    "langchain-text-splitters==0.3.8",
    "langgraph==0.3.34",
    "langgraph-checkpoint==2.0.26",
    "langgraph-prebuilt==0.1.8",
    "langgraph-sdk==0.1.70",
    "langsmith==0.1.147",
    "litellm==1.71.1",
    "llm==0.25",
    "logfire==3.16.1",
    "loguru==0.7.3",
    "magicattr==0.1.6",
    "Mako==1.3.10",
    "markdown-it-py==3.0.0",
    "MarkupSafe==3.0.2",
    "marshmallow==3.26.1",
    "mdurl==0.1.2",
    "more-itertools==10.7.0",
    "mpmath==1.3.0",
    "msgpack==1.1.0",
    "multidict==6.4.3",
    "multiprocess==0.70.16",
    "mypy_extensions==1.1.0",
    "networkx==3.4.2",
    "numpy==2.2.5",
    "openai==1.82.0",
    "opentelemetry-api==1.33.1",
    "opentelemetry-exporter-otlp-proto-common==1.33.1",
    "opentelemetry-exporter-otlp-proto-http==1.33.1",
    "opentelemetry-instrumentation==0.54b1",
    "opentelemetry-instrumentation-asgi==0.54b1",
    "opentelemetry-instrumentation-fastapi==0.54b1",
    "opentelemetry-proto==1.33.1",
    "opentelemetry-sdk==1.33.1",
    "opentelemetry-semantic-conventions==0.54b1",
    "opentelemetry-util-http==0.54b1",
    "optuna==4.3.0",
    "orjson==3.10.18",
    "ormsgpack==1.10.0",
    "packaging==24.2",
    "pandas==2.2.3",
    "pbs-installer==2025.4.9",
    "pillow==11.2.1",
    "pkginfo==********",
    "platformdirs==4.3.8",
    "playwright==1.52.0",
    "pluggy==1.5.0",
    "poetry==2.1.3",
    "poetry-core==2.1.3",
    "postgrest==1.0.1",
    "propcache==0.3.1",
    "proto-plus==1.26.1",
    "protobuf (>=5.26.1,<6.0.0)",
    "puremagic==1.29",
    "pyarrow==20.0.0",
    "pyasn1==0.6.1",
    "pyasn1_modules==0.4.2",
    "pycparser==2.22",
    "pydantic==2.11.3",
    "pydantic-settings==2.9.1",
    "pydantic_core==2.33.1",
    "pyee==13.0.0",
    "Pygments==2.19.1",
    "PyJWT==2.10.1",
    "pypdf==5.4.0",
    "PyPDF2==3.0.1",
    "pyproject_hooks==1.2.0",
    "pytest==8.3.5",
    "pytest-dotenv==0.5.2",
    "pytest-mock==3.14.0",
    "python-dateutil==2.9.0.post0",
    "python-dotenv==1.1.0",
    "python-multipart==0.0.20",
    "python-ulid==3.0.0",
    "pytz==2025.2",
    "PyYAML==6.0.2",
    "RapidFuzz==3.13.0",
    "realtime==2.4.2",
    "referencing==0.36.2",
    "regex==2024.11.6",
    "requests==2.32.3",
    "requests-toolbelt==1.0.0",
    "rich (>=13.9.4,<14.0.0)",
    "rpds-py==0.25.1",
    "rsa==4.9.1",
    "safetensors==0.5.3",
    "scikit-learn==1.6.1",
    "scipy==1.15.2",
    "sentence-transformers==4.1.0",
    "setuptools==79.0.1",
    "shellingham==1.5.4",
    "six==1.17.0",
    "sniffio==1.3.1",
    "soupsieve==2.7",
    "SQLAlchemy==2.0.41",
    "sqlite-fts4==1.0.3",
    "sqlite-migrate==0.1b0",
    "sqlite-utils==3.38",
    "stagehand-py==0.3.6",
    "starlette==0.46.2",
    "storage3==0.11.3",
    "StrEnum==0.4.15",
    "supabase==2.15.0",
    "supafunc==0.9.4",
    "sympy==1.13.3",
    "tabulate==0.9.0",
    "tenacity>=9.0.0,<10.0.0",
    "threadpoolctl==3.6.0",
    "tiktoken==0.9.0",
    "tokenizers==0.21.1",
    "tomlkit==0.13.2",
    "torch==2.7.0",
    "tqdm==4.67.1",
    "transformers==4.51.3",
    "trove-classifiers==2025.5.9.12",
    "typer==0.15.4",
    "typing-inspect==0.9.0",
    "typing-inspection==0.4.0",
    "typing_extensions==4.13.2",
    "tzdata==2025.2",
    "tzlocal==5.3.1",
    "ujson==5.10.0",
    "urllib3==2.4.0",
    "uvicorn==0.34.2",
    "verdict==0.2.2",
    "virtualenv==20.31.2",
    "websockets==14.2",
    "wrapt==1.17.2",
    "xattr==1.1.4",
    "xxhash==3.5.0",
    "yarl==1.20.0",
    "zipp==3.22.0",
    "zstandard==0.23.0",
    "trio (>=0.30.0,<0.31.0)",
    "python-docx (>=1.2.0,<2.0.0)",
    "mcp (>=1.9.4,<2.0.0)",
    "fastmcp (>=2.9.0,<3.0.0)"
]

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
[tool.poetry]
package-mode = false
[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
pytest-asyncio = "^1.0.0"
httpx = "^0.28.1"
