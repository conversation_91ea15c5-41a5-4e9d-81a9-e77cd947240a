# #!/usr/bin/env python3
# """
# Final working stdio MCP server for Agent Gateway
# """
# import sys
# import json
# import logging
# from typing import Dict, Any, Optional
#
# # Configure logging to stderr
# logging.basicConfig(
#     level=logging.INFO,
#     format='[MCP] %(message)s',
#     stream=sys.stderr,
#     force=True
# )
# logger = logging.getLogger(__name__)
#
# class FinalMCPServer:
#     def __init__(self):
#         self.initialized = False
#
#     def handle_request(self, request: Dict[str, Any]) -> Optional[Dict[str, Any]]:
#         """Handle a single request"""
#         method = request.get("method")
#         request_id = request.get("id")
#
#         if method == "initialize":
#             logger.info("Handling initialize request")
#             self.initialized = True
#             return {
#                 "jsonrpc": "2.0",
#                 "id": request_id,
#                 "result": {
#                     "protocolVersion": "2024-11-05",
#                     "capabilities": {
#                         "tools": {"listChanged": False},
#                         "prompts": {"listChanged": False}
#                     },
#                     "serverInfo": {
#                         "name": "final-mcp-server",
#                         "version": "1.0.0"
#                     }
#                 }
#             }
#
#         elif method == "notifications/initialized":
#             logger.info("Initialized notification received")
#             return None  # No response for notifications
#
#         elif method == "tools/list":
#             logger.info("Handling tools/list request")
#             return {
#                 "jsonrpc": "2.0",
#                 "id": request_id,
#                 "result": {
#                     "tools": [
#                         {
#                             "name": "calculate_resume_similarity",
#                             "description": "Calculate similarity between resume and job description"
#                         },
#                         {
#                             "name": "evaluate_resume_detailed",
#                             "description": "Perform detailed resume evaluation"
#                         },
#                         {
#                             "name": "generate_hiring_decision",
#                             "description": "Generate hiring decision"
#                         }
#                     ]
#                 }
#             }
#
#         elif method == "tools/call":
#             tool_name = request["params"]["name"]
#             arguments = request["params"]["arguments"]
#
#             logger.info(f"🔧 Handling tools/call: {tool_name}")
#
#             # Process the tool call
#             if tool_name == "calculate_resume_similarity":
#                 result = {
#                     "similarity_score": 0.85,
#                     "interpretation": "Strong match",
#                     "percentage": 85.0
#                 }
#             elif tool_name == "evaluate_resume_detailed":
#                 result = {
#                     "overall_score": 88,
#                     "skills_match": {"score": 90, "explanation": "Strong technical skills"},
#                     "experience_relevance": {"score": 85, "explanation": "Relevant experience"}
#                 }
#             elif tool_name == "generate_hiring_decision":
#                 similarity_score = arguments.get("similarity_score", 0.75)
#                 if similarity_score > 0.8:
#                     result = {"decision": "RECOMMEND", "reason": "Strong candidate with excellent match"}
#                 elif similarity_score > 0.6:
#                     result = {"decision": "CONSIDER", "reason": "Good candidate, worth interviewing"}
#                 else:
#                     result = {"decision": "REJECT", "reason": "Insufficient match"}
#             else:
#                 logger.error(f"Unknown tool: {tool_name}")
#                 return {
#                     "jsonrpc": "2.0",
#                     "id": request_id,
#                     "error": {
#                         "code": -32601,
#                         "message": f"Unknown tool: {tool_name}"
#                     }
#                 }
#
#             logger.info(f"✅ Tool completed: {str(result)[:100]}...")
#
#             return {
#                 "jsonrpc": "2.0",
#                 "id": request_id,
#                 "result": {
#                     "content": [
#                         {
#                             "type": "text",
#                             "text": json.dumps(result)
#                         }
#                     ]
#                 }
#             }
#
#         else:
#             logger.warning(f"Unknown method: {method}")
#             return {
#                 "jsonrpc": "2.0",
#                 "id": request_id,
#                 "error": {
#                     "code": -32601,
#                     "message": f"Method not found: {method}"
#                 }
#             }
#
#     def run(self):
#         """Run the MCP server"""
#         logger.info("🚀 Final MCP Server starting...")
#
#         # Make stdout unbuffered
#         sys.stdout.reconfigure(line_buffering=True)
#
#         try:
#             for line in sys.stdin:
#                 line = line.strip()
#                 if not line:
#                     continue
#
#                 # Parse JSON request
#                 try:
#                     request = json.loads(line)
#                     logger.info(f"RECV: {json.dumps(request)[:100]}...")
#                 except json.JSONDecodeError as e:
#                     logger.error(f"Invalid JSON: {e}")
#                     continue
#
#                 # Handle request
#                 try:
#                     response = self.handle_request(request)
#
#                     # Send response
#                     if response:
#                         response_json = json.dumps(response)
#                         print(response_json, flush=True)
#                         logger.info(f"SENT: {response_json[:100]}...")
#
#                         # Ensure output is flushed
#                         sys.stdout.flush()
#
#                 except Exception as e:
#                     logger.error(f"Error handling request: {e}")
#                     # Send error response
#                     error_response = {
#                         "jsonrpc": "2.0",
#                         "id": request.get("id"),
#                         "error": {
#                             "code": -32603,
#                             "message": str(e)
#                         }
#                     }
#                     print(json.dumps(error_response), flush=True)
#
#         except Exception as e:
#             logger.error(f"Fatal error: {e}")
#             import traceback
#             logger.error(f"Traceback: {traceback.format_exc()}")
#         finally:
#             logger.info("MCP Server shutdown complete")
#
# if __name__ == "__main__":
#     server = FinalMCPServer()
#     server.run()
#!/usr/bin/env python3
"""
Bulletproof MCP Server - Guaranteed to work with Agent Gateway
Fixed JSON format and stdio communication
"""
import json
import sys
import os
import time

# Ensure unbuffered output for Agent Gateway
os.environ['PYTHONUNBUFFERED'] = '1'
sys.stdout.reconfigure(line_buffering=True)
sys.stderr.reconfigure(line_buffering=True)


def log(message):
    """Log to stderr only"""
    print(f"[MCP] {message}", file=sys.stderr, flush=True)


def send_json(data):
    """Send JSON to stdout with exact Agent Gateway format"""
    try:
        # Use compact JSON with no spaces - exactly what Agent Gateway expects
        json_str = json.dumps(data, separators=(',', ':'), ensure_ascii=True)
        print(json_str)

        # Force immediate flush and sync
        sys.stdout.flush()
        os.fsync(sys.stdout.fileno())

        # Longer delay to ensure Agent Gateway processes the response completely
        time.sleep(0.3)

        log(f"SENT: {json_str}")
    except Exception as e:
        log(f"ERROR sending JSON: {e}")
        # Try to continue anyway
        pass


def main():
    """Main server loop"""
    log("Bulletproof MCP Server starting...")

    # Track initialization state
    initialized = False
    request_count = 0

    try:
        while True:
            # Read line from stdin
            line = sys.stdin.readline()

            if not line:
                log("EOF - shutting down")
                break

            line = line.strip()
            if not line:
                continue

            log(f"RECV: {line}")

            try:
                request = json.loads(line)
                request_count += 1
                log(f"Processing request #{request_count}")
            except json.JSONDecodeError as e:
                log(f"JSON decode error: {e}")
                continue

            # Extract request details
            method = request.get("method", "")
            request_id = request.get("id")
            params = request.get("params", {})

            # Handle notifications (no response needed)
            if request_id is None:
                if method == "notifications/initialized":
                    log("Initialized notification received")
                continue

            # Generate response based on method
            response = None

            if method == "initialize":
                if initialized:
                    log("WARNING: Multiple initialize requests - sending same result and exiting")
                    # Send response and exit to avoid PollSendError
                else:
                    log("Handling first initialize request")
                    initialized = True

                response = {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {
                            "tools": {},
                            "prompts": {}
                        },
                        "serverInfo": {
                            "name": "bulletproof-mcp-server",
                            "version": "1.0.0"
                        }
                    }
                }

            elif method == "tools/list":
                log("Handling tools/list request")
                response = {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "tools": [
                            {
                                "name": "process_hr_query",
                                "description": "Process HR queries",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "query": {"type": "string"}
                                    },
                                    "required": ["query"]
                                }
                            },
                            {
                                "name": "calculate_resume_similarity",
                                "description": "Calculate resume similarity",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "resume_text": {"type": "string"},
                                        "job_description": {"type": "string"}
                                    },
                                    "required": ["resume_text", "job_description"]
                                }
                            },
                            {
                                "name": "evaluate_resume_detailed",
                                "description": "Evaluate resume in detail",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "resume_text": {"type": "string"},
                                        "job_description": {"type": "string"}
                                    },
                                    "required": ["resume_text", "job_description"]
                                }
                            },
                            {
                                "name": "generate_hiring_decision",
                                "description": "Generate hiring decision",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "similarity_score": {"type": "number"},
                                        "evaluation_json": {"type": "object"}
                                    },
                                    "required": ["similarity_score", "evaluation_json"]
                                }
                            }
                        ]
                    }
                }

            elif method == "tools/call":
                log("Handling tools/call request")
                tool_name = params.get("name", "")
                arguments = params.get("arguments", {})

                # Simple mock responses
                if tool_name == "process_hr_query":
                    query = arguments.get("query", "")
                    result_text = f"HR Query processed: {query}. This is a mock response from bulletproof MCP server."
                elif tool_name == "calculate_resume_similarity":
                    result_text = '{"similarity_score": 0.78, "match_details": "Good technical skills match"}'
                elif tool_name == "evaluate_resume_detailed":
                    result_text = '{"overall_score": 82, "skills_match": {"score": 85, "explanation": "Strong technical background"}, "experience_relevance": {"score": 80, "explanation": "Relevant industry experience"}}'
                elif tool_name == "generate_hiring_decision":
                    result_text = "CONSIDER - Strong candidate with good technical skills and relevant experience. Recommend for technical interview."
                else:
                    result_text = f"Unknown tool: {tool_name}"

                response = {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "content": [
                            {
                                "type": "text",
                                "text": result_text
                            }
                        ]
                    }
                }

            elif method == "prompts/list":
                log("Handling prompts/list request")
                response = {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "prompts": [
                            {
                                "name": "classify_hr_query_intent",
                                "description": "Classify HR query intent",
                                "arguments": [
                                    {
                                        "name": "query",
                                        "description": "Query to classify",
                                        "required": True
                                    }
                                ]
                            }
                        ]
                    }
                }

            elif method == "prompts/get":
                log("Handling prompts/get request")
                prompt_name = params.get("name", "")
                arguments = params.get("arguments", {})

                if prompt_name == "classify_hr_query_intent":
                    query = arguments.get("query", "").lower()

                    # Simple classification
                    if any(word in query for word in ["resume", "cv", "job description", "similarity"]):
                        intent = "resume_vs_jd"
                    elif any(word in query for word in ["candidate", "evaluate", "assessment"]):
                        intent = "portal_candidate_eval"
                    else:
                        intent = "general_hr_query"

                    response = {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "messages": [
                                {
                                    "role": "user",
                                    "content": {
                                        "type": "text",
                                        "text": intent
                                    }
                                }
                            ]
                        }
                    }
                else:
                    response = {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "error": {
                            "code": -32601,
                            "message": f"Unknown prompt: {prompt_name}"
                        }
                    }

            else:
                log(f"Unknown method: {method}")
                response = {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32601,
                        "message": f"Method not found: {method}"
                    }
                }

            # Send response
            if response:
                send_json(response)

    except KeyboardInterrupt:
        log("Keyboard interrupt received")
    except Exception as e:
        log(f"Fatal error: {e}")
        import traceback
        traceback.print_exc(file=sys.stderr)

    log("Server shutting down")


if __name__ == "__main__":
    main()
