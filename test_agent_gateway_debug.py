#!/usr/bin/env python3
"""
Debug script to test Agent Gateway directly
"""
import asyncio
import httpx
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_agent_gateway():
    """Test Agent Gateway directly"""
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        logger.info("🚀 Testing Agent Gateway connection...")
        
        # Step 1: Get session ID
        logger.info("📡 Getting session ID...")
        try:
            async with client.stream(
                "GET",
                "http://127.0.0.1:3000/sse",
                headers={"Accept": "text/event-stream"}
            ) as response:
                
                if response.status_code != 200:
                    logger.error(f"❌ SSE connection failed: {response.status_code}")
                    return
                
                session_id = None
                async for line in response.aiter_lines():
                    logger.info(f"📥 SSE: {line}")
                    if line.startswith("data: ") and "sessionId=" in line:
                        data = line[6:]  # Remove "data: " prefix
                        session_id = data.split("sessionId=")[1].strip()
                        logger.info(f"✅ Got session ID: {session_id}")
                        break
                
                if not session_id:
                    logger.error("❌ No session ID received")
                    return
                    
        except Exception as e:
            logger.error(f"❌ Error getting session ID: {e}")
            return
        
        # Step 2: Initialize session
        logger.info("🔄 Initializing session...")
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}, "prompts": {}},
                "clientInfo": {"name": "TestClient", "version": "1.0.0"}
            }
        }
        
        try:
            response = await client.post(
                f"http://127.0.0.1:3000/sse?sessionId={session_id}",
                json=init_request,
                headers={"Content-Type": "application/json"}
            )
            logger.info(f"📤 Initialize response: {response.status_code}")
            
            if response.status_code not in [200, 202]:
                logger.error(f"❌ Initialize failed: {response.status_code}")
                return
                
        except Exception as e:
            logger.error(f"❌ Error initializing: {e}")
            return
        
        # Step 3: Call a tool
        logger.info("🔧 Calling tool...")
        tool_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/call",
            "params": {
                "name": "calculate_resume_similarity",
                "arguments": {
                    "resume_text": "Python developer",
                    "job_description": "Python role"
                }
            }
        }
        
        try:
            response = await client.post(
                f"http://127.0.0.1:3000/sse?sessionId={session_id}",
                json=tool_request,
                headers={"Content-Type": "application/json"}
            )
            logger.info(f"📤 Tool call response: {response.status_code}")
            
            if response.status_code not in [200, 202]:
                logger.error(f"❌ Tool call failed: {response.status_code}")
                return
                
        except Exception as e:
            logger.error(f"❌ Error calling tool: {e}")
            return
        
        # Step 4: Listen for response
        logger.info("👂 Listening for tool response...")
        try:
            async with client.stream(
                "GET",
                f"http://127.0.0.1:3000/sse?sessionId={session_id}",
                headers={"Accept": "text/event-stream"},
                timeout=15.0
            ) as sse_response:
                
                line_count = 0
                async for line in sse_response.aiter_lines():
                    line_count += 1
                    logger.info(f"📥 Response {line_count}: {line}")
                    
                    if line.startswith("data: ") and line != "data: ":
                        data = line[6:]  # Remove "data: " prefix
                        try:
                            response_data = json.loads(data)
                            if "result" in response_data:
                                logger.info("✅ SUCCESS! Tool response received!")
                                logger.info(f"🎯 Result: {response_data['result']}")
                                return
                            elif "error" in response_data:
                                logger.error(f"❌ Tool error: {response_data['error']}")
                                return
                        except json.JSONDecodeError:
                            logger.info(f"📄 Non-JSON data: {data}")
                    
                    if line_count > 20:  # Limit
                        logger.warning("⏰ Timeout - too many lines")
                        break
                        
        except Exception as e:
            logger.error(f"❌ Error listening for response: {e}")
            return
        
        logger.error("❌ No tool response received")

if __name__ == "__main__":
    asyncio.run(test_agent_gateway())
