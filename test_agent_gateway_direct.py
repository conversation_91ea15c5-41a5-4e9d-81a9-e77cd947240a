#!/usr/bin/env python3
"""
Direct test of Agent Gateway to see if it's working
"""
import asyncio
import httpx
import json

async def test_agent_gateway_direct():
    """Test Agent Gateway directly without redirects"""
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        print("🔍 Step 1: Getting session ID...")
        
        # Get session ID
        response = await client.get("http://127.0.0.1:3000/sse")
        session_id = response.headers.get("x-session-id")
        if not session_id:
            # Try to extract from response
            content = response.text
            if "sessionId=" in content:
                session_id = content.split("sessionId=")[1].split("&")[0].split("\n")[0].strip()
        
        print(f"✅ Got session ID: {session_id[:8]}...")
        
        print("🔍 Step 2: Initialize session...")
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}, "prompts": {}},
                "clientInfo": {"name": "DirectTest", "version": "1.0.0"}
            }
        }
        
        init_response = await client.post(
            f"http://127.0.0.1:3000/sse?sessionId={session_id}",
            json=init_request,
            headers={"Content-Type": "application/json"}
        )
        print(f"📥 Initialize response: {init_response.status_code}")
        
        print("🔍 Step 3: Send tool request...")
        tool_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/call",
            "params": {
                "name": "calculate_resume_similarity",
                "arguments": {
                    "resume_text": "Python developer",
                    "job_description": "Python role"
                }
            }
        }
        
        tool_response = await client.post(
            f"http://127.0.0.1:3000/sse?sessionId={session_id}",
            json=tool_request,
            headers={"Content-Type": "application/json"}
        )
        print(f"📥 Tool response: {tool_response.status_code}")
        
        print("🔍 Step 4: Listen for response (no redirects)...")
        
        # Wait a bit for processing
        await asyncio.sleep(2.0)
        
        # Listen for response
        async with client.stream(
            "GET",
            f"http://127.0.0.1:3000/sse?sessionId={session_id}",
            headers={"Accept": "text/event-stream"},
            timeout=10.0
        ) as sse_response:
            
            line_count = 0
            async for line in sse_response.aiter_lines():
                line_count += 1
                print(f"📥 Line {line_count}: {line}")
                
                if line.startswith("data: ") and not line.startswith("data: ?sessionId="):
                    data = line[6:]
                    if data.strip() not in ["ping", "[DONE]", ""]:
                        try:
                            response_data = json.loads(data)
                            if "result" in response_data:
                                print(f"✅ SUCCESS! Got result: {response_data['result']}")
                                return
                            elif "error" in response_data:
                                print(f"❌ Got error: {response_data['error']}")
                                return
                        except json.JSONDecodeError:
                            print(f"⚠️ Could not parse: {data}")
                
                if line_count > 20:
                    print("⏰ Timeout - no response received")
                    break

if __name__ == "__main__":
    asyncio.run(test_agent_gateway_direct())
