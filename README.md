**Recruiter Assistant**

**Overview**

This is an AI-powered tool designed to evaluate resumes against job descriptions. This system checks the similarity between a resume and a job description, evaluates the skills and experience match, and provides detailed feedback on the candidate's fit for the position. It also gives recommendations for improving the resume. The backend is built using FastAPI, and it utilizes models like Sentence Transformers and Gemini to perform the evaluation.

**Features**

**Resume Evaluation**: Compares the candidate's resume with a job description.

**Similarity Score**: Provides a similarity score based on how well the resume matches the job description.

**Deep Evaluation via DSPy**
Next, a DSPy module performs a detailed evaluation. It generates structured feedback covering:

Skill match (present vs. missing skills)

Relevance of experience

Overall score (0-100)

Recommendations for improvement

This step gives a holistic understanding of how suitable the candidate is beyond just keyword matching.

**LLM-Based Decision Making (LLManager)**

Now comes the decision-making phase. A language model (like GPT-4) receives:

The similarity score

The detailed evaluation results

The model then decides:

Whether the candidate should be auto-approved

Or flagged for manual review
It also generates a short reason for the decision.

5. Output
The system returns:

A decision (auto-approved or needs-review)

A reason explaining the choice

This result can be used by recruiters to quickly prioritize or filter candidates.

**Detailed Feedback**: Gives feedback on the match of skills and qualifications, the relevance of experience, and suggestions for resume improvement.

**Result Storage**: Saves evaluation results to Supabase for later review.

**Technologies Used**

**FastAPI**: For building the REST API.

**LangGraph**: For managing the state machine flow and chaining evaluation tasks.

**Sentence Transformers**: For embedding and comparing text similarity between resumes and job descriptions.

**Supabase**: For storing evaluation results.

**Pydantic**: For data validation and modeling.

**Set up environment variables:**

Create a .env file and add your Google API key, supabase url and supabase api key.


GOOGLE_API_KEY=your-google-api-key


**Start the FastAPI server:**

uvicorn main:app --reload

**API Endpoint:**

**POST /evaluate**

Evaluates a resume against a job description. The job description and resume file (PDF format) must be provided.

**Request:**

{

    "job_description": "Job description text here",

    "resume_file": "Resume file (PDF)"
}

**Response:**


{

    "success": true,
    "data": {
        "resume_text": "Extracted text from the resume",
        "job_description": "Provided job description",
        "evaluation_json": {
            "overall_score": 85,
            "evaluation": {
                "skills_match": {
                    "score": 80,
                    "present_skills": ["Skill1", "Skill2"],
                    "missing_skills": ["Skill3"],
                    "explanation": "Explanation about skills match."
                },
                "experience_relevance": {
                    "score": 90,
                    "explanation": "Explanation about experience relevance."
                }
            },
            "recommendations": [
                "Recommendation 1",
                "Recommendation 2"
            ],
            "Similarity_Score": "0.85"
        }
    }
}

