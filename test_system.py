#!/usr/bin/env python3
"""
Test the complete system integration
"""
import asyncio
import sys
import os

# Add project root to path
sys.path.append('/Users/<USER>/Documents/CES_MCP')

async def test_mcp_client():
    """Test the MCP client directly"""
    print("🧪 Testing MCP client...")
    
    try:
        from app.mcp_module.client import gateway, classify_intent, process_hr_query
        
        # Test intent classification
        print("\n1. Testing intent classification...")
        intent = await classify_intent("What are best practices for technical interviews?")
        print(f"   ✅ Intent: {intent}")
        
        # Test HR query processing
        print("\n2. Testing HR query processing...")
        response = await process_hr_query("What are the key skills for a Python developer?")
        print(f"   ✅ Response length: {len(response)} chars")
        print(f"   📝 Response preview: {response[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP client test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_graph_recruiter():
    """Test the graph recruiter"""
    print("\n🧪 Testing graph recruiter...")
    
    try:
        from app.orchestrator.graph_recruiter import process_unified_request
        
        # Test general HR query
        print("\n1. Testing general HR query...")
        result = await process_unified_request(
            query="What are best practices for conducting technical interviews?",
            resume_text=None,
            job_description=None,
            url=None,
            intent=None
        )
        
        print(f"   ✅ Intent: {result.intent}")
        print(f"   📝 Response: {result.response[:100] if result.response else 'No response'}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Graph recruiter test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests"""
    print("🚀 Starting system integration tests...\n")
    
    # Test MCP client
    mcp_success = await test_mcp_client()
    
    # Test graph recruiter
    graph_success = await test_graph_recruiter()
    
    print(f"\n📊 Test Results:")
    print(f"   MCP Client: {'✅ PASS' if mcp_success else '❌ FAIL'}")
    print(f"   Graph Recruiter: {'✅ PASS' if graph_success else '❌ FAIL'}")
    
    if mcp_success and graph_success:
        print("\n🎉 All tests passed! System is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Check the logs above for details.")

if __name__ == "__main__":
    asyncio.run(main())
