#!/usr/bin/env python3
"""
Test if Agent Gateway can see and call MCP tools
"""
import asyncio
import httpx
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_gateway_tools():
    """Test Agent Gateway tools integration"""
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        logger.info("🚀 Testing Agent Gateway tools...")
        
        # Step 1: Get session ID
        logger.info("📡 Getting session ID...")
        async with client.stream(
            "GET",
            "http://127.0.0.1:3000/sse",
            headers={"Accept": "text/event-stream"}
        ) as response:
            
            session_id = None
            async for line in response.aiter_lines():
                logger.info(f"📥 SSE: {line}")
                if line.startswith("data: ") and "sessionId=" in line:
                    data = line[6:]  # Remove "data: " prefix
                    session_id = data.split("sessionId=")[1].strip()
                    logger.info(f"✅ Got session ID: {session_id}")
                    break
            
            if not session_id:
                logger.error("❌ No session ID received")
                return
        
        # Step 2: Initialize session
        logger.info("🔄 Initializing session...")
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}, "prompts": {}},
                "clientInfo": {"name": "TestClient", "version": "1.0.0"}
            }
        }
        
        response = await client.post(
            f"http://127.0.0.1:3000/sse?sessionId={session_id}",
            json=init_request,
            headers={"Content-Type": "application/json"}
        )
        logger.info(f"📤 Initialize response: {response.status_code}")
        
        # Step 3: List tools
        logger.info("📋 Listing tools...")
        tools_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list",
            "params": {}
        }
        
        response = await client.post(
            f"http://127.0.0.1:3000/sse?sessionId={session_id}",
            json=tools_request,
            headers={"Content-Type": "application/json"}
        )
        logger.info(f"📤 Tools list response: {response.status_code}")
        
        # Step 4: Listen for responses
        logger.info("👂 Listening for responses...")
        async with client.stream(
            "GET",
            f"http://127.0.0.1:3000/sse?sessionId={session_id}",
            headers={"Accept": "text/event-stream"},
            timeout=15.0
        ) as sse_response:
            
            line_count = 0
            async for line in sse_response.aiter_lines():
                line_count += 1
                logger.info(f"📥 Response {line_count}: {line}")
                
                if line.startswith("data: ") and line != "data: ":
                    data = line[6:]  # Remove "data: " prefix
                    try:
                        response_data = json.loads(data)
                        if "result" in response_data and "tools" in response_data["result"]:
                            tools = response_data["result"]["tools"]
                            logger.info(f"✅ SUCCESS! Found {len(tools)} tools:")
                            for tool in tools:
                                logger.info(f"  🔧 {tool['name']}: {tool['description']}")
                            return True
                        elif "error" in response_data:
                            logger.error(f"❌ Error: {response_data['error']}")
                            return False
                    except json.JSONDecodeError:
                        logger.info(f"📄 Non-JSON data: {data}")
                
                if line_count > 20:  # Limit
                    logger.warning("⏰ Timeout - too many lines")
                    break
        
        logger.error("❌ No tools response received")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_gateway_tools())
    if result:
        print("✅ Agent Gateway tools integration working!")
    else:
        print("❌ Agent Gateway tools integration failed!")
