<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="4">
            <item index="0" class="java.lang.String" itemvalue="tqdm" />
            <item index="1" class="java.lang.String" itemvalue="torch" />
            <item index="2" class="java.lang.String" itemvalue="requests" />
            <item index="3" class="java.lang.String" itemvalue="torchaudio" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="E122" />
          <option value="E501" />
          <option value="E402" />
          <option value="E201" />
          <option value="E202" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N801" />
          <option value="N803" />
          <option value="N806" />
          <option value="N802" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyShadowingBuiltinsInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredNames">
        <list>
          <option value="id" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyTypeCheckerInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="sampleapp.models.Userpost.*" />
          <option value="loginapp.models.LoginappRegisterlogin.*" />
          <option value="rest_framework.status.HTTP_200_CREATED" />
          <option value="registerapp.views.error" />
          <option value="rest_framework.authtoken.models.Token.*" />
          <option value="registerapp.models.LoginappRegisterlogin.*" />
          <option value="roleapp.models.Officeproject.*" />
          <option value="apitaskapp.models.Sellermodel.objects" />
          <option value="apitaskapp.serializers.Seller_serialializererialializer" />
          <option value="django.db.models.Sellermodel" />
          <option value="apitaskapp.views.RiceSerializer" />
          <option value="apitaskapp.models.Rice.objects" />
          <option value="type.with_ui" />
          <option value="type.*" />
          <option value="rest_framework.generics.CreateAPIView.to_representation" />
          <option value="apitaskapp.models.Seller_model.objects" />
          <option value="permissionapp.models.CustomUser.generate_used_field" />
          <option value="permissionapp.models.CustomUser" />
          <option value="permissionapp.models.permissionapp" />
          <option value="permissionapp.models.cpermissionapp" />
          <option value="permissionapp.models.Products.*" />
          <option value="adminapp.models.Group.objects" />
          <option value="adminapp.models.Role.objects" />
          <option value="adminapp.models.Role.DoesNotExist" />
          <option value="gdrive_multiplefolder_attachment_extract.drive_service1" />
          <option value="gdrive_multiplefolder_attachment_extract.drive_service2" />
          <option value="langchain_document_loader_pro.langchain_document_loader_pro.views" />
          <option value="VivyaPms_app.views.CustomLoginView.*" />
          <option value="main.AuthHandler.*" />
          <option value="VivyaPms_app.models.Users.objects" />
          <option value="VivyaPms_app.models.RoomType.objects" />
          <option value="VivyaPms_app.models.RoomType.DoesNotExist" />
          <option value="VivyaPms_app.models.Room.objects" />
          <option value="VivyaPms_app.models.VivyapmsAppAuthtoken.objects" />
          <option value="VivyaPms_app.models.Property.objects" />
          <option value="VivyaPms_app.models.DocumentType.objects" />
          <option value="VivyaPms_app.models.VisaTypes.objects" />
          <option value="VivyaPms_app.models.MapRoomDocument.objects" />
          <option value="VivyaPms_app.models.DocumentDetails.objects" />
          <option value="Valianttinfo_app.models.CandidateInfoTemp.objects" />
          <option value="Valianttinfo_app.models.CheckDetails.objects" />
          <option value="Valianttinfo_app.models.PackageDetails.objects" />
          <option value="Valianttinfo_app.models.InputFieldsInformation.objects" />
          <option value="datetime.time.__sub__" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>