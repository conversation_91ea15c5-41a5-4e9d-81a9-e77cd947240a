#!/usr/bin/env python3
"""
Persistent stdio MCP server that stays connected
"""
import sys
import json
import logging
import signal
from typing import Dict, Any, Optional

# Configure logging to stderr
logging.basicConfig(
    level=logging.INFO,
    format='[MCP] %(message)s',
    stream=sys.stderr,
    force=True
)
logger = logging.getLogger(__name__)

class PersistentMCPServer:
    def __init__(self):
        self.initialized = False
        self.running = True
        
        # Handle signals gracefully
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
    def _signal_handler(self, signum, frame):
        logger.info(f"Received signal {signum}, shutting down gracefully")
        self.running = False
        
    def handle_request(self, request: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Handle a single request"""
        method = request.get("method")
        request_id = request.get("id")
        
        if method == "initialize":
            logger.info("Handling initialize request")
            self.initialized = True
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {"listChanged": False},
                        "prompts": {"listChanged": False}
                    },
                    "serverInfo": {
                        "name": "persistent-mcp-server",
                        "version": "1.0.0"
                    }
                }
            }
            
        elif method == "notifications/initialized":
            logger.info("Initialized notification received")
            return None  # No response for notifications
            
        elif method == "tools/list":
            logger.info("Handling tools/list request")
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "tools": [
                        {
                            "name": "calculate_resume_similarity",
                            "description": "Calculate similarity between resume and job description",
                            "inputSchema": {
                                "type": "object",
                                "properties": {
                                    "resume_text": {"type": "string"},
                                    "job_description": {"type": "string"}
                                },
                                "required": ["resume_text", "job_description"]
                            }
                        },
                        {
                            "name": "evaluate_resume_detailed",
                            "description": "Perform detailed resume evaluation",
                            "inputSchema": {
                                "type": "object",
                                "properties": {
                                    "resume_text": {"type": "string"},
                                    "job_description": {"type": "string"}
                                },
                                "required": ["resume_text", "job_description"]
                            }
                        },
                        {
                            "name": "generate_hiring_decision",
                            "description": "Generate hiring decision",
                            "inputSchema": {
                                "type": "object",
                                "properties": {
                                    "similarity_score": {"type": "number"},
                                    "evaluation_json": {"type": "object"}
                                },
                                "required": ["similarity_score", "evaluation_json"]
                            }
                        }
                    ]
                }
            }
            
        elif method == "tools/call":
            if not self.initialized:
                logger.error("Tool call received before initialization")
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32002,
                        "message": "Server not initialized"
                    }
                }
                
            tool_name = request["params"]["name"]
            arguments = request["params"]["arguments"]
            
            logger.info(f"🔧 Handling tools/call: {tool_name}")
            
            # Process the tool call
            if tool_name == "calculate_resume_similarity":
                result = {
                    "similarity_score": 0.85,
                    "interpretation": "Strong match",
                    "percentage": 85.0
                }
            elif tool_name == "evaluate_resume_detailed":
                result = {
                    "overall_score": 88,
                    "skills_match": {"score": 90, "explanation": "Strong technical skills"},
                    "experience_relevance": {"score": 85, "explanation": "Relevant experience"}
                }
            elif tool_name == "generate_hiring_decision":
                similarity_score = arguments.get("similarity_score", 0.75)
                if similarity_score > 0.8:
                    result = {"decision": "RECOMMEND", "reason": "Strong candidate with excellent match"}
                elif similarity_score > 0.6:
                    result = {"decision": "CONSIDER", "reason": "Good candidate, worth interviewing"}
                else:
                    result = {"decision": "REJECT", "reason": "Insufficient match"}
            else:
                logger.error(f"Unknown tool: {tool_name}")
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32601,
                        "message": f"Unknown tool: {tool_name}"
                    }
                }
            
            logger.info(f"✅ Tool completed: {str(result)[:100]}...")
            
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "content": [
                        {
                            "type": "text",
                            "text": json.dumps(result)
                        }
                    ]
                }
            }
            
        else:
            logger.warning(f"Unknown method: {method}")
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32601,
                    "message": f"Method not found: {method}"
                }
            }
    
    def run(self):
        """Run the persistent MCP server"""
        logger.info("🚀 Persistent MCP Server starting...")
        
        try:
            while self.running:
                try:
                    # Read line from stdin
                    line = sys.stdin.readline()
                    if not line:
                        # EOF - but don't exit, keep running
                        logger.info("EOF received, but staying alive...")
                        continue
                    
                    line = line.strip()
                    if not line:
                        continue
                    
                    # Parse JSON request
                    try:
                        request = json.loads(line)
                        logger.info(f"RECV: {json.dumps(request)[:100]}...")
                    except json.JSONDecodeError as e:
                        logger.error(f"Invalid JSON: {e}")
                        continue
                    
                    # Handle request
                    response = self.handle_request(request)
                    
                    # Send response
                    if response:
                        response_json = json.dumps(response)
                        print(response_json, flush=True)
                        logger.info(f"SENT: {response_json[:100]}...")
                        
                except Exception as e:
                    logger.error(f"Error handling request: {e}")
                    import traceback
                    logger.error(f"Traceback: {traceback.format_exc()}")
                    # Don't break on individual errors
                    continue
                    
        except KeyboardInterrupt:
            logger.info("Received interrupt, shutting down gracefully")
        except Exception as e:
            logger.error(f"Fatal error: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
        finally:
            logger.info("MCP Server shutdown complete")

if __name__ == "__main__":
    server = PersistentMCPServer()
    server.run()
