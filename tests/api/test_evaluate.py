# from fastapi.testclient import TestClient
# from app.main import app
#
# client = TestClient(app)
#
#
# def test_general_hr_query():
#     response = client.post(
#         "/api/hr-assistant-unified",
#         data={"query": "generate job description for java developer"}
#     )
#     print('response', response.json())
#     assert response.status_code == 200
#     data = response.json()
#     assert "intent" in data
#     assert "response" in data
#
#
# def test_resume_vs_jd_comparison():
#     job_description = "We are looking for a Python developer with experience in FastAPI, Docker, and PostgreSQL."
#     resume_pdf_path = "tests/api/Bhavanisha_Resume_012 (1).pdf"
#
#     with open(resume_pdf_path, "rb") as f:
#         files = {"resume_file": ("Bhavanisha_Resume.pdf", f, "application/pdf")}
#         data = {"job_description": job_description}
#
#         response = client.post(
#             "/api/hr-assistant-unified",
#             files=files,
#             data=data
#         )
#
#     print("Resume vs JD Comparison Response:", response.json())
#
#     assert response.status_code == 200
#     data = response.json()
#     assert data.get("intent") == "resume_vs_jd"
#     assert "similarity_score" in data
#     assert "evaluation_json" in data


import pytest
from app.orchestrator.graph_recruiter import process_unified_request

pytestmark = pytest.mark.asyncio

async def test_mcp_integration():
    query = "What are good hiring strategies for remote teams?"
    result = await process_unified_request(query=query)
    print("Intent:", result.intent)
    print("Response:", result.response)
    assert result.intent == "general_hr_query"
    assert result.response is not None
